
		This is the README file for WordNet 3.0

1. About WordNet

WordNet was developed at Princeton University's Cognitive Science
Laboratory under the direction of <PERSON>, <PERSON> University Professor of Psychology, Emeritus. Over the
years many linguists, lexicographers, students, and software engineers
have contributed to the project.

WordNet is an online lexical reference system.  Word forms in WordNet
are represented in their familiar orthography; word meanings are
represented by synonym sets (synsets) - lists of synonymous word forms
that are interchangeable in some context.  Two kinds of relations are
recognized: lexical and semantic.  Lexical relations hold between word
forms; semantic relations hold between word meanings.  

To learn more about WordNet, the book "WordNet: An Electronic Lexical
Database," containing an updated version of "Five Papers on WordNet"
and additional papers by WordNet users, is available from MIT Press:

    http://mitpress.mit.edu/book-home.tcl?isbn=026206197X

2. The WordNet Web Site

We maintain a Web site at:

http://wordnet.princeton.edu

Information about WordNet, access to our online interface, and the
various WordNet packages that you can download are available from our
web site.  All of the software documentation is available online, as
well as a FAQ.  On this site we also have information about other
applications that use WordNet. If you have an application that you
would like included, please send e-mail to the above address.

3. Contacting Us

Ongoing deveopment work and WordNet related projects are done by a
small group of researchers, lexicographers, and systems programmers.
Since our resources are VERY limited, we request that you please
confine correspondence to WordNet topics only.  Please check the
documentation, FAQ, and other resources for the answer to your
question or problem before contacting us.

If you have trouble installing or downloading WordNet, have a bug to
report, or any other problem, please refer to the online FAQ file
first.  If you can heal thyself, please do so.  The FAQ will be
updated over time.  And if you do find a previously unreported
problem, please use our Bug Report Form:

http://wordnet.princeton.edu/cgi-bin/bugsubmit.pl

When reporting a problem, please be as specific as possible, stating
the computer platform you are using, which interface you are using,
and the exact error.  The more details you can provide, the more
likely it is that you will get an answer. 

There is a WordNet user discussion group mailing list that we invite
our users to join.  Users use this list to ask questions of one
another, announce extensions to WordNet that they've developed, and
other topics of general usefulness to the user community.

Information on joining the user discussion list, reporting bugs and other
contact information is in found on our website at:

http://wordnet.princeton.edu/contact

4. Current Release

WordNet Version 3.0 is the latest version available for download.  Two
basic database packages are available - one for Windows and one for
Unix platforms (including Mac OS X).  See the file ChangeLog (Unix) or
CHANGES.txt (Windows) for a list of changes from previous versions.

WordNet packages can either be downloaded from our web site via:

http://wordnet.princeton.edu/obtain

The Windows package is a self-extracting archive that installs itself
when you double-click on it.

Beginning with Version 2.1, we changed the Unix package to a GNU Autotools
package.  The WordNet browser makes use of the open source Tcl and Tk
packages. Many systems come with either or both pre-installed.  If
your system doesn't (some systems have Tcl installed, but not Tk)
Tcl/Tk can be downloaded from:

http://www.tcl.tk/ 

Tcl and Tk must be installed BEFORE you compile WordNet. You must also
have a C compiler before installing Tcl/Tk or WordNet.  WordNet has
been built and tested with the GNU gcc compiler.  This is
pre-installed on most Unix systems, and can be downloaded from:

http://gcc.gnu.org/

See the file INSTALL for detailed WordNet installation instructions.


