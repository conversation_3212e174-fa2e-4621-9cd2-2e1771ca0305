#    Copyright 2024 Ha<PERSON> Zhang
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

# 导入类型提示相关的库，以增强代码的可读性和健壮性。
from typing import List, Optional, Tuple, Union, Dict
import torch
import torch.nn as nn
from torch.nn import CrossEntropyLoss

import transformers
# 导入Hugging Face Transformers库中的核心类，用于加载模型配置和模型本身。
from transformers import AutoConfig, AutoModelForCausalLM, LlamaConfig, LlamaModel, LlamaForCausalLM

from transformers.modeling_outputs import CausalLMOutputWithPast
from transformers.generation.utils import GenerateOutput

# 语法重难点：从项目内部模块导入
# 从 llava_arch.py 导入包含了多模态核心逻辑的基类。
# LlavaMetaModel 包含了视觉模块的定义（如 aeb_head）。
# LlavaMetaForCausalLM 包含了处理图文输入的核心方法（prepare_inputs_labels_for_multimodal）。
from llava.model.llava_arch import LlavaMetaModel, LlavaMetaForCausalLM
# 导入本项目所使用的语言模型基座：Qwen2。
from transformers import Qwen2Config, Qwen2Model, Qwen2ForCausalLM

# (被注释掉的代码，通常是开发者在探索不同实现时留下的痕迹)
# from .qwen.modeling_qwen import QWenLMHeadModel, QWenModel
# from .qwen.configuration_qwen import QWenConfig


# 定义一个名为 LlavaQwenConfig 的新配置类。
# 语法重难点：类继承
# 它继承自 Hugging Face 官方的 Qwen2Config，这意味着它自动获得了Qwen2模型的所有标准配置。
class LlavaQwenConfig(Qwen2Config):
    # model_type 是一个标识符，用于在Hugging Face生态系统中唯一地识别这个自定义模型。
    model_type = "llava_qwen"


# 定义一个名为 LlavaQwenModel 的新模型类。
# 语法重难点：多重继承 (Multiple Inheritance)
# 这个类同时继承了 LlavaMetaModel 和 Qwen2Model。
# 这使得 LlavaQwenModel 实例既拥有 LlavaMetaModel 赋予的多模态处理能力，
# 又拥有 Qwen2Model 强大的语言理解和生成能力。
class LlavaQwenModel(LlavaMetaModel, Qwen2Model):
    # 将此类的配置指向我们上面定义的 LlavaQwenConfig。
    config_class = LlavaQwenConfig

    # 构造函数
    def __init__(self, config: Qwen2Config):
        # 调用父类的构造函数，完成必要的初始化。
        super(LlavaQwenModel, self).__init__(config)


# 这是最终用于训练和推理的完整模型，包含了语言模型头（for Causal LM）。
class LlavaQwenForCausalLM(Qwen2ForCausalLM, LlavaMetaForCausalLM):
    config_class = LlavaQwenConfig

    def __init__(self, config):
        # 首先，调用父类 Qwen2ForCausalLM 的构造函数。
        Qwen2ForCausalLM.__init__(self, config)
        # 设置并覆盖一些配置，以确保其符合我们的自定义模型。
        config.model_type = "llava_qwen"
        config.rope_scaling = None

        # 程序逻辑：核心替换步骤
        # 将模型内部的 self.model 替换为我们上面定义的、具备多模态能力的 LlavaQwenModel。
        # 这是将一个纯文本LLM“升级”为多模态LLM的关键操作。
        self.model = LlavaQwenModel(config)
        
        # 定义语言模型头（LM Head），它负责将模型最后一层的输出转换为词汇表的概率。
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)
        
        # 调用 Hugging Face 提供的 post_init() 方法来完成权重的最终初始化。
        self.post_init()

    def get_model(self):
        return self.model

    # 语法重难点：方法重写 (Method Overriding)
    # 重写了父类的 forward 方法，以注入多模态处理逻辑。这是整个模型工作的核心。
    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        images: Optional[torch.FloatTensor] = None,
        image_sizes: Optional[List[List[int]]] = None,
        return_dict: Optional[bool] = None,
        output_actions: Optional[bool] = False,
        modalities: Optional[List[str]] = ["image"],
        dpo_forward: Optional[bool] = False,
        ego_action: Optional[torch.FloatTensor] = None, # 增加了ego_action参数来接收车辆的动作真值
        cache_position=None,
    ) -> Union[Tuple, CausalLMOutputWithPast]:

        # 程序逻辑：多模态输入准备
        # 如果模型调用时没有直接提供 inputs_embeds（即融合了图文的嵌入向量）...
        if inputs_embeds is None:
            # ...那么就调用继承自 LlavaMetaForCausalLM 的 prepare_inputs_labels_for_multimodal 方法。
            # 这个方法会处理传入的文本(input_ids)和图像(images)，将它们融合成一个单一的 inputs_embeds 张量。
            (input_ids, position_ids, attention_mask, past_key_values, inputs_embeds, labels) = self.prepare_inputs_labels_for_multimodal(input_ids, position_ids, attention_mask, past_key_values, labels, images, modalities, image_sizes)
        
        # 在准备好多模态输入后，调用父类（Qwen2ForCausalLM）的原始 forward 方法进行计算。
        outputs = super().forward(
                input_ids=input_ids,
                attention_mask=attention_mask,
                position_ids=position_ids,
                past_key_values=past_key_values,
                inputs_embeds=inputs_embeds,
                labels=labels,
                use_cache=use_cache,
                output_attentions=output_attentions,
                output_hidden_states=True,  # 强制要求输出所有隐藏层状态，以便后续计算AEB损失
                return_dict=return_dict,
            )
        
        # 程序逻辑：计算AEB损失（被注释掉的部分）
        # 这部分代码展示了作者最初可能的设计：在标准的语言模型损失之外，
        # 还计算一个AEB（自动紧急制动）相关的损失。
        # 1. `last_layer_hidden_states = outputs.hidden_states[-1]`: 获取模型最后一层的输出。
        # 2. `average_hidden_states = ...`: 对最后一层所有token的隐藏状态进行加权平均（使用attention_mask），得到一个能代表整个序列的向量。
        # 3. `aeb_loss, _ = self.decode_aeb(...)`: 将这个平均向量输入到 aeb_head 中，计算出制动信号的预测值，并与真值（ego_action）计算损失。
        # 4. `total_loss = sum(loss_dict.values())`: 将文本生成损失和AEB损失相加，得到总损失，用于联合优化。
        # 
        # (当前版本的代码注释掉了这部分，可能是在评估阶段不需要计算损失，或者损失计算逻辑被移到了别处)

        # 在评估模式下，我们直接返回模型的原始输出。
        return outputs
    
    # 语法重难点：装饰器 @torch.no_grad()
    # 告诉PyTorch在执行此方法时不需要计算和存储梯度，以节省计算资源。
    @torch.no_grad()
    def generate(
        self,
        inputs: Optional[torch.Tensor] = None,
        images: Optional[torch.Tensor] = None,
        image_sizes: Optional[torch.Tensor] = None,
        modalities: Optional[List[str]] = ["image"],
        **kwargs,
    ) -> Union[GenerateOutput, torch.LongTensor]:
        # (处理和传递generate函数的各种参数)
        position_ids = kwargs.pop("position_ids", None)
        attention_mask = kwargs.pop("attention_mask", None)
        if "inputs_embeds" in kwargs:
            raise NotImplementedError("`inputs_embeds` is not supported")

        # 程序逻辑：与 forward 方法类似，在生成文本之前，先处理好多模态输入。
        if images is not None:
            # 如果有图像，调用核心方法准备好多模态的 inputs_embeds。
            (inputs, position_ids, attention_mask, _, inputs_embeds, _) = self.prepare_inputs_labels_for_multimodal(inputs, position_ids, attention_mask, None, None, images, modalities, image_sizes=image_sizes)
        else:
            # 如果没有图像，正常处理文本输入。
            inputs_embeds = self.get_model().embed_tokens(inputs)

        # 调用父类的原始 generate 方法来生成文本。
        return super().generate(position_ids=position_ids, attention_mask=attention_mask, inputs_embeds=inputs_embeds, **kwargs)

    # 这是一个辅助函数，确保在文本生成的每一步中，图像信息都能被正确地传递。
    def prepare_inputs_for_generation(self, input_ids, past_key_values=None, inputs_embeds=None, **kwargs):
        images = kwargs.pop("images", None)
        image_sizes = kwargs.pop("image_sizes", None)
        inputs = super().prepare_inputs_for_generation(input_ids, past_key_values=past_key_values, inputs_embeds=inputs_embeds, **kwargs)
        if images is not None:
            inputs["images"] = images
        if image_sizes is not None:
            inputs["image_sizes"] = image_sizes
        return inputs

# 语法重难点：向Hugging Face框架注册自定义模型
# 告诉Hugging Face的AutoClass（如AutoConfig, AutoModelForCausalLM），
# 当在 from_pretrained 中遇到 "llava_qwen" 这个 model_type 时，
# 应该使用我们上面定义的 LlavaQwenConfig 和 LlavaQwenForCausalLM 类。
AutoConfig.register("llava_qwen", LlavaQwenConfig)
AutoModelForCausalLM.register(LlavaQwenConfig, LlavaQwenForCausalLM)