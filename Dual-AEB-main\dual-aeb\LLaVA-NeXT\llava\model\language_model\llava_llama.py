#    Copyright 2023 <PERSON><PERSON><PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.


from typing import List, Optional, Tuple, Union

import torch
import torch.nn as nn

from transformers import AutoConfig, AutoModelForCausalLM, LlamaConfig

from torch.nn import CrossEntropyLoss


# , LlamaModel, LlamaForCausalLM, GenerationConfig
# from .modeling_llama import LlamaModel, LlamaForCausalLM
from transformers import LlamaModel, LlamaForCausalLM
from transformers.modeling_outputs import CausalLMOutputWithPast
from transformers.generation.utils import GenerateOutput

# 语法重难点：从自定义模块导入
# 这里从项目内部的 llava.model.llava_arch 模块导入了两个关键的“元类”（MetaModel）。
# 这两个类包含了处理多模态输入的核心逻辑，它们将被“混入”（mix-in）到标准的Llama模型中。
from llava.model.llava_arch import LlavaMetaModel, LlavaMetaForCausalLM


# 定义一个名为 LlavaConfig 的新配置类。
# 语法重难点：类继承 (Class Inheritance)
# LlavaConfig 继承自 Hugging Face 的 LlamaConfig。
# 这意味着它拥有 LlamaConfig 的所有属性，同时我们可以在此基础上添加或修改一些新的配置。
class LlavaConfig(LlamaConfig):
    # model_type 是一个标识符，告诉Hugging Face框架这是一个自定义的 "llava_llama" 模型。
    model_type = "llava_llama"
    # 以下是一些与文本生成相关的默认参数设置。
    temperature: float = 0.0  # 设置为0.0意味着生成过程是确定性的，而不是随机的。
    max_new_tokens: int = 1024 # 生成新文本的最大长度。
    do_sample: bool = False # 关闭采样，与 temperature=0.0 配合使用，确保输出的确定性。
    top_p: Optional[float] = None


# 定义一个名为 LlavaLlamaModel 的新模型类。
# 语法重难点：多重继承 (Multiple Inheritance)
# 这个类同时继承了我们自定义的 LlavaMetaModel 和 Hugging Face 的 LlamaModel。
# 这是一种强大的技术，它使得 LlavaLlamaModel 同时拥有了：
# 1. LlavaMetaModel 的多模态处理能力（如视觉模块 aeb_head）。
# 2. LlamaModel 的核心Transformer结构（如embedding层、decoder层等）。
class LlavaLlamaModel(LlavaMetaModel, LlamaModel):
    # 将这个类的配置指向我们上面定义的 LlavaConfig。
    config_class = LlavaConfig

    # 构造函数，在创建类的实例时被调用。
    def __init__(self, config: LlamaConfig):
        # super(...) 调用父类的构造函数，以确保所有必要的初始化都已完成。
        super(LlavaLlamaModel, self).__init__(config)


# 这是最终对外暴露的模型类，用于实际的训练和推理。
# 它同样使用了多重继承，结合了 LlamaForCausalLM（带有语言模型头的Llama）和 LlavaMetaForCausalLM（处理多模态输入的元类）。
class LlavaLlamaForCausalLM(LlamaForCausalLM, LlavaMetaForCausalLM):
    config_class = LlavaConfig

    def __init__(self, config):
        # 首先，调用 LlamaForCausalLM 的构造函数进行标准初始化。
        LlamaForCausalLM.__init__(self, config)

        # 重新配置模型类型为自定义类型。
        config.model_type = "llava_llama"

        # 程序逻辑：替换核心模型
        # 这里是实现多模态能力的关键一步：
        # 它将父类中原本纯文本的 self.model 替换为我们上面定义的、具有多模态能力的 LlavaLlamaModel 实例。
        self.model = LlavaLlamaModel(config)
        
        # 定义语言模型头（LM Head），这是一个线性层，用于将模型最后的隐藏状态转换为词汇表上的概率分布。
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)
        
        # 调用 post_init() 来完成权重的最终处理和初始化。
        self.post_init()

    def get_model(self):
        return self.model

    # 语法重难点：方法重写 (Method Overriding)
    # 这是整个脚本中最核心的部分。我们重写了父类 LlamaForCausalLM 的 forward 方法，
    # 以便在标准的Transformer计算流程之前，先注入我们的多模态处理逻辑。
    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        images: Optional[torch.FloatTensor] = None,
        image_sizes: Optional[List[List[int]]] = None,
        return_dict: Optional[bool] = None,
        modalities: Optional[List[str]] = ["image"],
        dpo_forward: Optional[bool] = None,
        cache_position=None,
    ) -> Union[Tuple, CausalLMOutputWithPast]:

        # 程序逻辑：多模态输入处理
        # 正常情况下，模型接收的是 input_ids（文本token的ID）。
        # 如果 inputs_embeds 是 None，说明我们需要自己从 input_ids 和 images（图像）来构建它。
        if inputs_embeds is None:
            # 调用从 LlavaMetaForCausalLM 继承来的核心方法 prepare_inputs_labels_for_multimodal。
            # 这个方法会：
            # 1. 将图像（images）通过视觉编码器转换成视觉特征。
            # 2. 将文本（input_ids）转换成文本嵌入。
            # 3. 将视觉特征巧妙地“插入”到文本嵌入序列中，形成一个融合了图文信息的新的 inputs_embeds。
            # 这样，对于后续的语言模型来说，它看到的输入就仿佛是一句包含了特殊“图像单词”的句子。
            (input_ids, position_ids, attention_mask, past_key_values, inputs_embeds, labels) = self.prepare_inputs_labels_for_multimodal(input_ids, position_ids, attention_mask, past_key_values, labels, images, modalities, image_sizes)

        if dpo_forward:
            # (这部分逻辑用于特定的DPO训练，可以暂时忽略)
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                position_ids=position_ids,
                past_key_values=past_key_values,
                inputs_embeds=inputs_embeds,
                use_cache=use_cache,
                output_attentions=output_attentions,
                output_hidden_states=output_hidden_states,
                return_dict=return_dict,
            )

            hidden_states = outputs[0]
            logits = self.lm_head(hidden_states)
            return logits, labels

        else:
            # 程序逻辑：调用原始的 forward 方法
            # 在 inputs_embeds 准备好之后，我们调用父类（LlamaForCausalLM）的原始 forward 方法。
            # 因为此时的 inputs_embeds 已经包含了图像信息，所以标准的Transformer流程就能够自然地处理这些多模态输入了。
            return super().forward(
                input_ids=input_ids,
                attention_mask=attention_mask,
                position_ids=position_ids,
                past_key_values=past_key_values,
                inputs_embeds=inputs_embeds,
                labels=labels,
                use_cache=use_cache,
                output_attentions=output_attentions,
                output_hidden_states=output_hidden_states,
                return_dict=return_dict,
            )

    # 语法重难点：装饰器 @torch.no_grad()
    # 这个装饰器告诉PyTorch，在执行 generate 方法时，不需要计算梯度。
    # 这对于推理（生成文本）过程非常重要，因为它可以显著减少内存消耗并加快计算速度。
    @torch.no_grad()
    def generate(
        self,
        inputs: Optional[torch.Tensor] = None,
        images: Optional[torch.Tensor] = None,
        image_sizes: Optional[torch.Tensor] = None,
        modalities: Optional[List[str]] = ["image"],
        **kwargs,
    ) -> Union[GenerateOutput, torch.LongTensor]:
        # (这部分主要是在处理和传递参数)
        modalities = kwargs.pop("modalities", None) if "modalities" in kwargs and modalities is None else modalities
        position_ids = kwargs.pop("position_ids", None)
        attention_mask = kwargs.pop("attention_mask", None)
        if "inputs_embeds" in kwargs:
            raise NotImplementedError("`inputs_embeds` is not supported")

        # 程序逻辑：在生成文本前处理图像
        # 如果传入了图像（images is not None）...
        if images is not None:
            # ...就先调用 prepare_inputs_labels_for_multimodal 方法来生成包含图像信息的 inputs_embeds。
            (inputs, position_ids, attention_mask, _, inputs_embeds, _) = self.prepare_inputs_labels_for_multimodal(inputs, position_ids, attention_mask, None, None, images, modalities, image_sizes=image_sizes)
        else:
            # 如果没有图像，就正常地通过词嵌入层获取文本的 embeds。
            inputs_embeds = self.get_model().embed_tokens(inputs)

        # 最后，调用父类的原始 generate 方法，但这次传入的是我们精心构造的、可能包含图像信息的 inputs_embeds。
        return super().generate(position_ids=position_ids, attention_mask=attention_mask, inputs_embeds=inputs_embeds, **kwargs)

    # 这个辅助方法被 generate 函数在内部调用，
    # 它的作用是确保在生成过程的每一步，图像相关的参数（如 images 和 image_sizes）都能够被正确地传递下去。
    def prepare_inputs_for_generation(self, input_ids, past_key_values=None, inputs_embeds=None, **kwargs):
        images = kwargs.pop("images", None)
        image_sizes = kwargs.pop("image_sizes", None)
        inputs = super().prepare_inputs_for_generation(input_ids, past_key_values=past_key_values, inputs_embeds=inputs_embeds, **kwargs)
        if images is not None:
            inputs["images"] = images
        if image_sizes is not None:
            inputs["image_sizes"] = image_sizes
        return inputs

# 语法重难点：向Hugging Face框架注册自定义模型
# 这两行代码的作用是告诉 Hugging Face 的 AutoClass（如 AutoConfig, AutoModelForCausalLM），
# 当遇到 "llava_llama" 这个 model_type 时，应该使用我们上面定义的 LlavaConfig 和 LlavaLlamaForCausalLM 类来加载模型。
# 这使得用户可以用和官方模型一样的方式（例如 `AutoModelForCausalLM.from_pretrained(...)`）来加载这个自定义模型，非常方便。
AutoConfig.register("llava_llama", LlavaConfig)
AutoModelForCausalLM.register(LlavaConfig, LlavaLlamaForCausalLM)