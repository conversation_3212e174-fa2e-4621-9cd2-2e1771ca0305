# 导入必要的库和模块
import os  # 操作系统接口
import torch  # PyTorch深度学习框架
import torch.nn as nn  # 神经网络模块
from accelerate import Accelerator  # Hugging Face加速库，用于分布式训练
from accelerate.utils import InitProcessGroupKwargs, GradientAccumulationPlugin  # 加速器工具
from torch.utils.data import Dataset, Sampler, DataLoader  # PyTorch数据处理工具
from transformers.trainer import _is_peft_model  # 检查是否为PEFT模型
from transformers.modeling_utils import unwrap_model  # 模型解包工具
from transformers.models.auto.modeling_auto import (
    MODEL_FOR_CAUSAL_LM_MAPPING_NAMES,  # 因果语言模型映射名称
    MODEL_MAPPING_NAMES,  # 模型映射名称
)
import torch.distributed as dist  # PyTorch分布式训练
from transformers import Trainer  # Hugging Face训练器基类
from transformers.trainer import (
    is_sagemaker_mp_enabled,  # 检查SageMaker多进程是否启用
    get_parameter_names,  # 获取参数名称
    has_length,  # 检查数据集是否有长度
    ALL_LAYERNORM_LAYERS,  # 所有LayerNorm层
    logger,  # 日志记录器
    is_accelerate_available,  # 检查accelerate是否可用
    is_datasets_available,  # 检查datasets是否可用
    GradientAccumulationPlugin  # 梯度累积插件
)
from transformers.trainer_utils import seed_worker  # 随机种子工作器
from transformers.trainer_pt_utils import get_length_grouped_indices as get_length_grouped_indices_hf  # 长度分组索引
from typing import List, Optional  # 类型提示
from datetime import timedelta  # 时间间隔
import wandb  # Weights & Biases实验跟踪

# 条件导入：只有在accelerate可用时才导入相关模块
if is_accelerate_available():
    from accelerate import Accelerator, skip_first_batches, InitProcessGroupKwargs

# 条件导入：只有在datasets可用时才导入相关模块
if is_datasets_available():
    import datasets

from llava.utils import rank0_print  # LLaVA工具：仅在rank 0进程打印


def maybe_zero_3(param, ignore_status=False, name=None):
    """
    DeepSpeed ZeRO-3优化的参数处理函数
    在ZeRO-3模式下，参数可能被分片存储在不同设备上，需要特殊处理来获取完整参数

    Args:
        param: 模型参数张量
        ignore_status: 是否忽略参数状态检查
        name: 参数名称，用于调试
    Returns:
        处理后的参数张量（在CPU上）
    """
    from deepspeed import zero  # DeepSpeed ZeRO优化
    from deepspeed.runtime.zero.partition_parameters import ZeroParamStatus  # ZeRO参数状态

    # 检查参数是否有DeepSpeed ID（即是否被ZeRO管理）
    if hasattr(param, "ds_id"):
        # 检查参数状态是否可用
        if param.ds_status == ZeroParamStatus.NOT_AVAILABLE:
            if not ignore_status:
                print(name, "no ignore status")
        # 使用ZeRO的GatheredParameters上下文管理器收集分片参数
        with zero.GatheredParameters([param]):
            # 分离梯度、移到CPU、克隆参数
            param = param.data.detach().cpu().clone()
    else:
        # 如果不是ZeRO管理的参数，直接处理
        param = param.detach().cpu().clone()
    return param


def get_mm_adapter_state_maybe_zero_3(named_params, keys_to_match):
    """
    获取多模态适配器的状态字典，兼容ZeRO-3优化

    Args:
        named_params: 模型的命名参数迭代器
        keys_to_match: 需要匹配的键名列表
    Returns:
        包含匹配参数的状态字典
    """
    # 使用字典推导式筛选包含指定关键词的参数
    to_return = {k: t for k, t in named_params if any(key_match in k for key_match in keys_to_match)}
    # 对每个参数应用ZeRO-3兼容的处理，并移到CPU
    to_return = {k: maybe_zero_3(v, ignore_status=True, name=k).cpu() for k, v in to_return.items()}
    return to_return


def split_to_even_chunks(indices, lengths, num_chunks):
    """
    将索引列表分割成大致相等长度的块
    这是分布式训练中负载均衡的关键函数

    Args:
        indices: 要分割的索引列表
        lengths: 每个索引对应的长度
        num_chunks: 要分割成的块数（通常等于GPU数量）
    Returns:
        分割后的索引块列表
    """
    # 如果索引数量不能被块数整除，使用简单的轮询分配
    if len(indices) % num_chunks != 0:
        # 使用切片步长进行轮询分配：[0::num_chunks], [1::num_chunks], ...
        return [indices[i::num_chunks] for i in range(num_chunks)]

    # 计算每个块应该包含的索引数量
    num_indices_per_chunk = len(indices) // num_chunks

    # 初始化块列表和每个块的总长度
    chunks = [[] for _ in range(num_chunks)]  # 存储每个块的索引
    chunks_lengths = [0 for _ in range(num_chunks)]  # 存储每个块的总长度

    # 贪心算法：总是将索引分配给当前总长度最短的块
    for index in indices:
        # 找到总长度最短的块
        shortest_chunk = chunks_lengths.index(min(chunks_lengths))
        # 将索引添加到该块
        chunks[shortest_chunk].append(index)
        # 更新该块的总长度
        chunks_lengths[shortest_chunk] += lengths[index]
        # 如果该块已达到目标大小，将其长度设为无穷大，避免继续分配
        if len(chunks[shortest_chunk]) == num_indices_per_chunk:
            chunks_lengths[shortest_chunk] = float("inf")

    return chunks


def get_variable_length_grouped_indices(lengths, batch_size, world_size, megabatch_mult=8, generator=None):
    """
    获取变长分组的索引，用于处理不同长度的序列
    这种方法可以减少padding，提高训练效率

    Args:
        lengths: 每个样本的长度列表
        batch_size: 批次大小
        world_size: 分布式训练的进程数
        megabatch_mult: 超级批次倍数，用于控制分组粒度
        generator: 随机数生成器
    Returns:
        重新排列的索引列表
    """
    # 使用torch生成随机排列，确保分布式训练时的随机性一致
    indices = torch.randperm(len(lengths), generator=generator)
    # 按长度降序排列索引，长序列优先
    sorted_indices = sorted(range(len(lengths)), key=lambda i: lengths[i], reverse=True)
    # 计算超级批次大小
    megabatch_size = world_size * batch_size * megabatch_mult
    # 将排序后的索引分割成超级批次
    megabatches = [sorted_indices[i : i + megabatch_size] for i in range(0, len(lengths), megabatch_size)]
    # 在每个超级批次内部按随机顺序重新排序
    megabatches = [sorted(megabatch, key=lambda i: indices[i], reverse=True) for megabatch in megabatches]
    # 展平所有超级批次
    shuffled_indices = [i for megabatch in megabatches for i in megabatch]
    # 计算世界批次大小（所有进程的总批次大小）
    world_batch_size = world_size * batch_size
    # 将打乱的索引分割成最终的批次
    batches = [shuffled_indices[i : i + world_batch_size] for i in range(0, len(lengths), world_batch_size)]
    # 随机打乱批次顺序
    batch_indices = torch.randperm(len(batches), generator=generator)
    batches = [batches[i] for i in batch_indices]

    # 展平所有批次，返回最终的索引序列
    return [i for batch in batches for i in batch]


def get_modality_length_grouped_indices(lengths, batch_size, world_size, generator=None):
    """
    获取按模态和长度分组的索引
    用于多模态训练，将相似长度和相同模态的样本分组到同一批次

    分组策略：
    - 随机排列索引
    - 按超级批次大小分组
    - 在每个超级批次内按长度重新排序
    - 将最长的批次放在前面，以便尽早发现OOM错误

    Args:
        lengths: 长度列表，正数表示多模态样本，负数表示纯文本样本
        batch_size: 批次大小
        world_size: 分布式训练的进程数
        generator: 随机数生成器
    Returns:
        重新排列的索引列表
    """
    # 使用torch进行随机操作，确保分布式采样器的随机种子一致性
    assert all(l != 0 for l in lengths), "长度不应为零"

    # 如果所有样本都是同一模态，使用标准长度分组
    if all(l > 0 for l in lengths) or all(l < 0 for l in lengths):
        return get_length_grouped_indices(lengths, batch_size, world_size, generator=generator)

    # 分离多模态样本（正长度）和纯文本样本（负长度）
    mm_indices, mm_lengths = zip(*[(i, l) for i, l in enumerate(lengths) if l > 0])
    lang_indices, lang_lengths = zip(*[(i, -l) for i, l in enumerate(lengths) if l < 0])

    # 分别对多模态和纯文本样本进行长度分组
    mm_shuffle = [mm_indices[i] for i in get_length_grouped_indices(mm_lengths, batch_size, world_size, generator=None)]
    lang_shuffle = [lang_indices[i] for i in get_length_grouped_indices(lang_lengths, batch_size, world_size, generator=None)]

    # 计算超级批次大小
    megabatch_size = world_size * batch_size
    # 将打乱后的索引分割成超级批次
    mm_megabatches = [mm_shuffle[i : i + megabatch_size] for i in range(0, len(mm_shuffle), megabatch_size)]
    lang_megabatches = [lang_shuffle[i : i + megabatch_size] for i in range(0, len(lang_shuffle), megabatch_size)]

    # 处理最后不完整的批次
    last_mm = mm_megabatches[-1]
    last_lang = lang_megabatches[-1]
    additional_batch = last_mm + last_lang  # 合并最后的不完整批次

    # 合并除最后一个批次外的所有超级批次
    megabatches = mm_megabatches[:-1] + lang_megabatches[:-1]
    # 随机打乱超级批次的顺序
    megabatch_indices = torch.randperm(len(megabatches), generator=generator)
    megabatches = [megabatches[i] for i in megabatch_indices]

    # 如果有额外的批次，添加到末尾
    if len(additional_batch) > 0:
        megabatches.append(sorted(additional_batch))

    # 展平所有超级批次，返回最终的索引序列
    return [i for megabatch in megabatches for i in megabatch]


def get_length_grouped_indices(lengths, batch_size, world_size, generator=None, merge=True):
    """
    Return a list of indices so that each slice of `batch_size` consecutive indices correspond to elements of similar
    lengths. To do this, the indices are:

    - randomly permuted
    - grouped in mega-batches of size `mega_batch_mult * batch_size`
    - reorder by length in each mega-batch

    The result is the concatenation of all mega-batches, with the batch of `batch_size` containing the element of
    maximum length placed first, so that an OOM happens sooner rather than later.
    """

    # We need to use torch for the random part as a distributed sampler will set the random seed for torch.
    indices = torch.randperm(len(lengths), generator=generator)
    megabatch_size = world_size * batch_size
    megabatches = [indices[i : i + megabatch_size].tolist() for i in range(0, len(lengths), megabatch_size)]
    megabatches = [sorted(megabatch, key=lambda i: lengths[i], reverse=True) for megabatch in megabatches]
    megabatches = [split_to_even_chunks(megabatch, lengths, world_size) for megabatch in megabatches]

    return [i for megabatch in megabatches for batch in megabatch for i in batch]


def get_length_grouped_indices_auto_single(lengths, batch_size, world_size, generator=None):
    indices = get_length_grouped_indices_hf(lengths, batch_size * world_size, generator=generator)

    megabatch_size = world_size * batch_size
    megabatches = [indices[i : i + megabatch_size] for i in range(0, len(lengths), megabatch_size)]
    megabatches = [sorted(megabatch, key=lambda i: lengths[i], reverse=True) for megabatch in megabatches]
    megabatches = [split_to_even_chunks(megabatch, lengths, world_size) for megabatch in megabatches]

    # We need to use torch for the random part as a distributed sampler will set the random seed for torch.
    batch_indices = torch.randperm(len(megabatches), generator=generator)
    megabatches = [megabatches[i] for i in batch_indices]

    return [i for megabatch in megabatches for batch in megabatch for i in batch]


def get_modality_length_grouped_indices_auto(lengths, batch_size, world_size, generator=None):
    # We need to use torch for the random part as a distributed sampler will set the random seed for torch.
    assert all(l != 0 for l in lengths), "Should not have zero length."
    if all(l > 0 for l in lengths) or all(l < 0 for l in lengths):
        # all samples are in the same modality
        return get_length_grouped_indices_auto_single(lengths, batch_size, world_size, generator=generator)
    mm_indices, mm_lengths = zip(*[(i, l) for i, l in enumerate(lengths) if l > 0])
    lang_indices, lang_lengths = zip(*[(i, -l) for i, l in enumerate(lengths) if l < 0])

    mm_shuffle = [mm_indices[i] for i in get_length_grouped_indices_auto_single(mm_lengths, batch_size, world_size, generator=None)]
    lang_shuffle = [lang_indices[i] for i in get_length_grouped_indices_auto_single(lang_lengths, batch_size, world_size, generator=None)]
    megabatch_size = world_size * batch_size
    mm_megabatches = [mm_shuffle[i : i + megabatch_size] for i in range(0, len(mm_shuffle), megabatch_size)]
    lang_megabatches = [lang_shuffle[i : i + megabatch_size] for i in range(0, len(lang_shuffle), megabatch_size)]

    last_mm = mm_megabatches[-1]
    last_lang = lang_megabatches[-1]
    additional_batch = last_mm + last_lang
    megabatches = mm_megabatches[:-1] + lang_megabatches[:-1]
    megabatch_indices = torch.randperm(len(megabatches), generator=generator)
    megabatches = [megabatches[i] for i in megabatch_indices]

    # FIXME: Hard code to avoid last batch mixed with different modalities
    # if len(additional_batch) > 0:
    #     megabatches.append(sorted(additional_batch))

    return [i for megabatch in megabatches for i in megabatch]


class LengthGroupedSampler(Sampler):
    """
    长度分组采样器
    将数据集中长度相似的样本分组到同一批次，同时保持一定的随机性
    这样可以减少padding，提高训练效率和内存利用率

    支持多种分组策略：
    - 标准长度分组
    - 变长分组
    - 按模态分组
    - 自动模态分组
    """

    def __init__(
        self,
        batch_size: int,  # 批次大小
        world_size: int,  # 分布式训练的进程数
        lengths: Optional[List[int]] = None,  # 每个样本的长度列表
        generator=None,  # 随机数生成器
        variable_length: bool = False,  # 是否使用变长分组
        group_by_modality: bool = False,  # 是否按模态分组
        group_by_modality_auto: bool = False,  # 是否使用自动模态分组
    ):
        """
        初始化长度分组采样器

        Args:
            batch_size: 每个批次的样本数
            world_size: 分布式训练的总进程数
            lengths: 样本长度列表，必须提供
            generator: 随机数生成器，用于确保分布式训练的一致性
            variable_length: 是否启用变长分组策略
            group_by_modality: 是否按模态进行分组
            group_by_modality_auto: 是否使用自动模态分组
        """
        if lengths is None:
            raise ValueError("必须提供长度列表")

        self.batch_size = batch_size
        self.world_size = world_size
        self.lengths = lengths
        self.generator = generator
        self.variable_length = variable_length
        self.group_by_modality = group_by_modality
        self.group_by_modality_auto = group_by_modality_auto

    def __len__(self):
        """返回数据集的总长度"""
        return len(self.lengths)

    def __iter__(self):
        """
        生成采样索引的迭代器
        根据配置选择不同的分组策略
        """
        if self.variable_length:
            # 变长分组不支持模态分组
            assert not self.group_by_modality, "变长分组不支持与模态分组同时使用"
            indices = get_variable_length_grouped_indices(
                self.lengths, self.batch_size, self.world_size, generator=self.generator
            )
        else:
            if self.group_by_modality:
                # 标准模态分组
                indices = get_modality_length_grouped_indices(
                    self.lengths, self.batch_size, self.world_size, generator=self.generator
                )
            elif self.group_by_modality_auto:
                # 自动模态分组
                indices = get_modality_length_grouped_indices_auto(
                    self.lengths, self.batch_size, self.world_size, generator=self.generator
                )
            else:
                # 标准长度分组
                indices = get_length_grouped_indices_auto_single(
                    self.lengths, self.batch_size, self.world_size, generator=self.generator
                )
        return iter(indices)


class LLaVATrainer(Trainer):
    """
    LLaVA专用训练器类
    继承自Hugging Face的Trainer类，针对多模态训练进行了优化

    主要功能：
    - 支持多模态数据的长度分组采样
    - 优化的加速器配置
    - 自定义优化器设置
    - 多模态适配器的保存和加载
    - 损失计算和日志记录
    """

    def create_accelerator_and_postprocess(self):
        """
        创建和配置加速器对象
        设置分布式训练、梯度累积、DeepSpeed等高级功能
        """
        # 配置梯度累积参数
        grad_acc_kwargs = {"num_steps": self.args.gradient_accumulation_steps}
        grad_acc_kwargs["sync_with_dataloader"] = False  # 不与数据加载器同步
        gradient_accumulation_plugin = GradientAccumulationPlugin(**grad_acc_kwargs)

        # 设置进程组初始化参数，超时时间设为52周（实际上是无限超时）
        accelerator_kwargs = InitProcessGroupKwargs(timeout=timedelta(weeks=52))
        rank0_print("设置NCCL超时为无限，避免运行错误")

        # 创建加速器对象，这是分布式训练的核心
        self.accelerator = Accelerator(
            dispatch_batches=self.args.dispatch_batches,  # 是否分发批次
            split_batches=self.args.split_batches,        # 是否分割批次
            deepspeed_plugin=self.args.deepspeed_plugin,  # DeepSpeed插件
            gradient_accumulation_plugin=gradient_accumulation_plugin,  # 梯度累积插件
            kwargs_handlers=[accelerator_kwargs]  # 额外的关键字参数处理器
        )

        # 某些训练器类需要使用`gather`而不是`gather_for_metrics`，因此存储一个标志
        self.gather_function = self.accelerator.gather_for_metrics

        # 检测DeepSpeed和FSDP是否启用
        self.is_deepspeed_enabled = getattr(self.accelerator.state, "deepspeed_plugin", None) is not None
        self.is_fsdp_enabled = getattr(self.accelerator.state, "fsdp_plugin", None) is not None

        # 加速器创建后的配置
        if self.is_fsdp_enabled:
            # 配置FSDP（Fully Sharded Data Parallel）插件
            fsdp_plugin = self.accelerator.state.fsdp_plugin
            # 限制all_gather操作的频率
            fsdp_plugin.limit_all_gathers = self.args.fsdp_config.get("limit_all_gathers", fsdp_plugin.limit_all_gathers)

            # 检查accelerate版本是否支持激活检查点
            if is_accelerate_available("0.23.0"):
                fsdp_plugin.activation_checkpointing = self.args.fsdp_config.get("activation_checkpointing", fsdp_plugin.activation_checkpointing)
                # 确保FSDP的激活检查点和训练参数的梯度检查点不同时启用
                if fsdp_plugin.activation_checkpointing and self.args.gradient_checkpointing:
                    raise ValueError(
                        "FSDP配置中的activation_checkpointing和训练参数中的gradient_checkpointing "
                        "不能同时设置为True。使用FSDP时请使用FSDP的activation_checkpointing逻辑。"
                    )

        # 如果启用了DeepSpeed但没有HF DeepSpeed配置，则传播参数到DeepSpeed
        if self.is_deepspeed_enabled and getattr(self.args, "hf_deepspeed_config", None) is None:
            self.propagate_args_to_deepspeed()

    def _get_train_sampler(self) -> Optional[torch.utils.data.Sampler]:
        """
        获取训练数据采样器
        根据配置选择不同的采样策略，优化多模态训练效率

        Returns:
            训练采样器实例，如果数据集没有长度则返回None
        """
        # 如果没有训练数据集或数据集没有长度，返回None
        if self.train_dataset is None or not has_length(self.train_dataset):
            return None

        # 按长度分组采样
        if self.args.group_by_length:
            lengths = self.train_dataset.lengths
            return LengthGroupedSampler(
                self.args.train_batch_size,  # 批次大小
                # 世界大小乘以梯度累积步数，用于正确的分布式采样
                world_size=self.args.world_size * self.args.gradient_accumulation_steps,
                lengths=lengths,
            )
        # 按模态和长度分组采样
        elif self.args.group_by_modality_length:
            lengths = self.train_dataset.modality_lengths  # 使用模态长度
            return LengthGroupedSampler(
                self.args.train_batch_size,
                world_size=self.args.world_size * self.args.gradient_accumulation_steps,
                lengths=lengths,
                group_by_modality=True,  # 启用模态分组
            )
        # 自动按模态和长度分组采样
        elif self.args.group_by_modality_length_auto:
            lengths = self.train_dataset.modality_lengths
            return LengthGroupedSampler(
                self.args.train_batch_size,
                world_size=self.args.world_size * self.args.gradient_accumulation_steps,
                lengths=lengths,
                group_by_modality_auto=True,  # 启用自动模态分组
            )
        # 变长分组采样
        elif self.args.group_by_varlen:
            lengths = self.train_dataset.lengths
            return LengthGroupedSampler(
                # 变长分组需要考虑梯度累积步数
                self.args.train_batch_size * self.args.gradient_accumulation_steps,
                world_size=self.args.world_size * self.args.gradient_accumulation_steps,
                lengths=lengths,
                variable_length=True,  # 启用变长分组
            )
        else:
            # 使用父类的默认采样器
            return super()._get_train_sampler()

    def get_train_dataloader(self) -> DataLoader:
        """
        创建训练数据加载器

        返回训练用的DataLoader实例。如果train_dataset没有实现__len__，将不使用采样器；
        否则使用随机采样器（如果需要，会适配分布式训练）。

        子类可以重写此方法来注入自定义行为。

        Returns:
            配置好的训练数据加载器
        """
        if self.train_dataset is None:
            raise ValueError("训练器需要训练数据集")

        train_dataset = self.train_dataset
        data_collator = self.data_collator

        # 如果使用Hugging Face datasets库，移除未使用的列
        if is_datasets_available() and isinstance(train_dataset, datasets.Dataset):
            train_dataset = self._remove_unused_columns(train_dataset, description="training")
        else:
            # 否则从数据整理器中移除未使用的列
            data_collator = self._get_collator_with_removed_columns(data_collator, description="training")

        # 配置数据加载器参数
        dataloader_params = {
            "batch_size": self._train_batch_size,  # 批次大小
            "collate_fn": data_collator,  # 数据整理函数
            "num_workers": self.args.dataloader_num_workers,  # 工作进程数
            "pin_memory": self.args.dataloader_pin_memory,  # 是否固定内存
            "persistent_workers": self.args.dataloader_persistent_workers,  # 是否持久化工作进程
        }

        # 如果不是可迭代数据集，添加额外的参数
        if not isinstance(train_dataset, torch.utils.data.IterableDataset):
            dataloader_params["sampler"] = self._get_train_sampler()  # 采样器
            dataloader_params["drop_last"] = self.args.dataloader_drop_last  # 是否丢弃最后一个不完整批次
            dataloader_params["worker_init_fn"] = seed_worker  # 工作进程初始化函数
            # 预取因子：每个工作进程预取的批次数
            dataloader_params["prefetch_factor"] = self.args.dataloader_num_workers * 2 if self.args.dataloader_num_workers != 0 else None

        # 使用加速器准备数据加载器，这会自动处理分布式训练的配置
        dataloader = self.accelerator.prepare(DataLoader(train_dataset, **dataloader_params))

        return dataloader

    def create_optimizer(self):
        """
        创建优化器

        为多模态模型提供合理的默认优化器配置。支持不同模块使用不同的学习率，
        这对于多模态训练非常重要（如视觉编码器和投影器可能需要不同的学习率）。

        如果想使用其他优化器，可以在Trainer初始化时通过`optimizers`参数传递，
        或者在子类中重写此方法。
        """
        # 如果启用了SageMaker多进程，使用父类的优化器创建方法
        if is_sagemaker_mp_enabled():
            return super().create_optimizer()

        opt_model = self.model

        if self.optimizer is None:
            # 获取需要权重衰减的参数名称（排除LayerNorm层）
            decay_parameters = get_parameter_names(opt_model, ALL_LAYERNORM_LAYERS)
            # 排除bias参数，因为通常不对bias应用权重衰减
            decay_parameters = [name for name in decay_parameters if "bias" not in name]

            # 创建学习率映射，为不同模块设置不同的学习率
            lr_mapper = {}
            if self.args.mm_projector_lr is not None:
                lr_mapper["mm_projector"] = self.args.mm_projector_lr  # 多模态投影器学习率
            if self.args.mm_vision_tower_lr is not None:
                lr_mapper["vision_tower"] = self.args.mm_vision_tower_lr  # 视觉塔学习率

            # 如果有特殊学习率的模块，创建分组参数
            if len(lr_mapper) > 0:
                # 找到需要特殊学习率的参数
                special_lr_parameters = [
                    name for name, _ in opt_model.named_parameters()
                    if any(module_keyword in name for module_keyword in lr_mapper)
                ]

                # 创建优化器参数组：普通参数（使用默认学习率）
                optimizer_grouped_parameters = [
                    {
                        # 需要权重衰减的普通参数
                        "params": [p for n, p in opt_model.named_parameters()
                                 if (n in decay_parameters and n not in special_lr_parameters and p.requires_grad)],
                        "weight_decay": self.args.weight_decay,
                    },
                    {
                        # 不需要权重衰减的普通参数
                        "params": [p for n, p in opt_model.named_parameters()
                                 if (n not in decay_parameters and n not in special_lr_parameters and p.requires_grad)],
                        "weight_decay": 0.0,
                    },
                ]

                # 为每个特殊模块添加参数组
                for module_keyword, lr in lr_mapper.items():
                    module_parameters = [name for name, _ in opt_model.named_parameters() if module_keyword in name]
                    optimizer_grouped_parameters.extend([
                        {
                            # 特殊模块中需要权重衰减的参数
                            "params": [p for n, p in opt_model.named_parameters()
                                     if (n in decay_parameters and n in module_parameters and p.requires_grad)],
                            "weight_decay": self.args.weight_decay,
                            "lr": lr,  # 使用特殊学习率
                        },
                        {
                            # 特殊模块中不需要权重衰减的参数
                            "params": [p for n, p in opt_model.named_parameters()
                                     if (n not in decay_parameters and n in module_parameters and p.requires_grad)],
                            "weight_decay": 0.0,
                            "lr": lr,  # 使用特殊学习率
                        },
                    ])
            else:
                # 如果没有特殊学习率的模块，使用简单的分组
                optimizer_grouped_parameters = [
                    {
                        # 需要权重衰减的参数
                        "params": [p for n, p in opt_model.named_parameters()
                                 if (n in decay_parameters and p.requires_grad)],
                        "weight_decay": self.args.weight_decay,
                    },
                    {
                        # 不需要权重衰减的参数
                        "params": [p for n, p in opt_model.named_parameters()
                                 if (n not in decay_parameters and p.requires_grad)],
                        "weight_decay": 0.0,
                    },
                ]

            # 获取优化器类和参数
            optimizer_cls, optimizer_kwargs = Trainer.get_optimizer_cls_and_kwargs(self.args)

            # 创建优化器实例
            self.optimizer = optimizer_cls(optimizer_grouped_parameters, **optimizer_kwargs)

            # 如果使用8位Adam优化器，需要特殊处理嵌入层
            if optimizer_cls.__name__ == "Adam8bit":
                import bitsandbytes

                # 获取bitsandbytes的全局优化管理器
                manager = bitsandbytes.optim.GlobalOptimManager.get_instance()

                skipped = 0  # 跳过的参数数量统计
                # 遍历模型的所有模块
                for module in opt_model.modules():
                    # 对于嵌入层，使用32位精度而不是8位
                    if isinstance(module, nn.Embedding):
                        # 计算跳过的参数数量
                        skipped += sum({p.data_ptr(): p.numel() for p in module.parameters()}.values())
                        logger.info(f"跳过 {module}: {skipped/2**20}M 参数")
                        # 注册模块覆盖，强制使用32位优化
                        manager.register_module_override(module, "weight", {"optim_bits": 32})
                        logger.debug(f"bitsandbytes: 将在fp32精度下优化 {module}")
                logger.info(f"总共跳过: {skipped/2**20}M 参数")

        return self.optimizer

    def _save_checkpoint(self, model, trial, metrics=None):
        """
        保存检查点

        如果只微调多模态适配器，则只保存适配器权重；
        否则保存完整模型。这样可以大大减少存储空间。

        Args:
            model: 要保存的模型
            trial: 试验对象（用于超参数搜索）
            metrics: 评估指标
        """
        # 检查是否只微调多模态MLP适配器或特定的可调部分
        if getattr(self.args, "tune_mm_mlp_adapter", False) or (
            hasattr(self.args, "mm_tunable_parts") and
            (len(self.args.mm_tunable_parts.split(",")) == 1 and
             ("mm_mlp_adapter" in self.args.mm_tunable_parts or "mm_vision_resampler" in self.args.mm_tunable_parts))
        ):
            from transformers.trainer_utils import PREFIX_CHECKPOINT_DIR

            # 创建检查点文件夹名称
            checkpoint_folder = f"{PREFIX_CHECKPOINT_DIR}-{self.state.global_step}"

            # 获取输出目录
            run_dir = self._get_output_dir(trial=trial)
            output_dir = os.path.join(run_dir, checkpoint_folder)

            # 只保存适配器相关的权重
            keys_to_match = ["mm_projector", "vision_resampler"]  # 多模态投影器和视觉重采样器
            if getattr(self.args, "use_im_start_end", False):
                # 如果使用图像开始/结束标记，也保存相关的嵌入层
                keys_to_match.extend(["embed_tokens", "embed_in"])

            # 获取匹配的权重，兼容ZeRO-3优化
            weight_to_save = get_mm_adapter_state_maybe_zero_3(self.model.named_parameters(), keys_to_match)

            # 只在主进程中保存（避免多进程重复保存）
            if self.args.local_rank == 0 or self.args.local_rank == -1:
                # 保存模型配置
                self.model.config.save_pretrained(output_dir)
                # 保存适配器权重
                torch.save(weight_to_save, os.path.join(output_dir, f"mm_projector.bin"))
        else:
            # 如果不是只微调适配器，使用父类的完整保存方法
            super(LLaVATrainer, self)._save_checkpoint(model, trial, metrics)

    def _save(self, output_dir: Optional[str] = None, state_dict=None):
        """
        保存模型状态

        如果只微调多模态MLP适配器，则跳过保存（因为已在_save_checkpoint中处理）；
        否则使用父类的保存方法。

        Args:
            output_dir: 输出目录
            state_dict: 状态字典
        """
        if getattr(self.args, "tune_mm_mlp_adapter", False):
            # 如果只微调适配器，不需要额外保存
            pass
        else:
            # 使用父类的保存方法
            super(LLaVATrainer, self)._save(output_dir, state_dict)
    

    def compute_loss(self, model, inputs, return_outputs=False):
        """
        计算训练损失

        这是训练器计算损失的核心方法。默认情况下，所有模型在第一个元素中返回损失。
        子类可以重写此方法以实现自定义行为。

        Args:
            model: 要训练的模型
            inputs: 输入数据字典
            return_outputs: 是否返回模型输出
        Returns:
            损失值，如果return_outputs=True则返回(损失, 输出)元组
        """
        # 如果有标签平滑器且输入中包含标签，提取标签
        if self.label_smoother is not None and "labels" in inputs:
            labels = inputs.pop("labels")
        else:
            labels = None

        # 前向传播获取模型输出
        outputs = model(**inputs)

        # 保存过去的状态（如果存在）
        # TODO: 这需要在后续版本中修复和清理
        if self.args.past_index >= 0:
            self._past = outputs[self.args.past_index]

        # 计算损失
        if labels is not None:
            # 如果有标签，使用标签平滑器计算损失
            unwrapped_model = unwrap_model(model)

            # 检查是否为PEFT模型（参数高效微调模型）
            if _is_peft_model(unwrapped_model):
                model_name = unwrapped_model.base_model.model._get_name()
            else:
                model_name = unwrapped_model._get_name()

            # 对于因果语言模型，需要移位标签
            if model_name in MODEL_FOR_CAUSAL_LM_MAPPING_NAMES.values():
                loss = self.label_smoother(outputs, labels, shift_labels=True)
            else:
                loss = self.label_smoother(outputs, labels)
        else:
            # 如果没有标签，从模型输出中提取损失
            if isinstance(outputs, dict) and "loss" not in outputs:
                raise ValueError(
                    f"模型没有从输入中返回损失，只有以下键: "
                    f"{','.join(outputs.keys())}. 作为参考，它接收到的输入是 {','.join(inputs.keys())}."
                )
            # 不使用.loss属性，因为模型可能返回元组而不是ModelOutput
            loss = outputs["loss"] if isinstance(outputs, dict) else outputs[0]

        # 如果输出是字典，记录所有损失项
        if isinstance(outputs, dict):
            loss_log_dict = {}
            # 记录所有损失相关的值
            for key, value in outputs.items():
                if hasattr(value, 'item'):  # 确保是张量
                    loss_log_dict[key] = value.item()
            # 添加学习率到日志
            loss_log_dict["llava_lr"] = self._get_learning_rate()
            # 记录到日志系统
            self.log(loss_log_dict)

        # 根据return_outputs参数返回相应的结果
        return (loss, outputs) if return_outputs else loss

    # 以下是注释掉的日志记录方法，可用于自定义日志记录
    # def log(self, logs: dict):
    #     """自定义日志记录方法"""
    #     rank = dist.get_rank() if dist.is_initialized() else 0
    #     if self.args.report_to and rank in [0, -1]:
    #         self.log_to_wandb(logs)

    # def log_to_wandb(self, logs: dict):
    #     """记录到Weights & Biases"""
    #     wandb.log(logs)