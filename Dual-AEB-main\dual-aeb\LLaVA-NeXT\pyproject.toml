[tool.black]
line-length = 240

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "llava"
version = "1.7.0.dev0"
description = "Towards GPT-4 like large language and visual assistant."
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
]

[project.optional-dependencies]
standalone = [
    "shortuuid",
    "httpx==0.24.0",
    "einops",
    "ftfy",
]

train = [
    "llava[standalone]",
    "open_clip_torch",
    "fastapi",
    "gradio==3.35.2",
    "markdown2[all]",
    "numpy",
    "requests",
    "sentencepiece",
    "torch==2.3.0",
    "torchvision==0.18.0",
    "uvicorn",
    "wandb==0.16.5",
    "deepspeed==0.12.2",
    "peft==0.4.0",
    "accelerate>=0.29.1",
    "tokenizers~=0.15.2",
    "bitsandbytes==0.41.0",
    "scikit-learn==1.2.2",
    "sentencepiece~=0.1.99",
    "einops==0.6.1",
    "einops-exts==0.0.4",
    "gradio_client==0.2.9",
    "pydantic==1.10.8",
    "timm",
    "hf_transfer",
    "decord",
    "datasets==2.16.1",
    "tyro",
    "scipy",
    "urllib3<=2.0.0"
]

[project.urls]
"Homepage" = "https://llava-vl.github.io"
"Bug Tracker" = "https://github.com/haotian-liu/LLaVA/issues"

[tool.setuptools.packages.find]
exclude = [
    "assets*",
    "benchmark*",
    "docs",
    "dist*",
    "playground*",
    "scripts*",
    "tests*",
    "checkpoints*",
    "project_checkpoints*",
    "debug_checkpoints*",
    "mlx_configs*",
    "wandb*",
    "notebooks*",
]

[tool.wheel]
exclude = [
    "assets*",
    "benchmark*",
    "docs",
    "dist*",
    "playground*",
    "scripts*",
    "tests*",
    "checkpoints*",
    "project_checkpoints*",
    "debug_checkpoints*",
    "mlx_configs*",
    "wandb*",
    "notebooks*",
]