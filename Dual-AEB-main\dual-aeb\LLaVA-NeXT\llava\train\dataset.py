# 导入必要的库和模块
import os  # 操作系统接口
import copy  # 深拷贝工具
from dataclasses import dataclass  # 数据类装饰器
import json  # JSON数据处理
from typing import Dict, Sequence  # 类型提示
from PIL import Image, ImageFile  # Python图像处理库
import numpy as np  # 数值计算库
import time  # 时间处理
import random  # 随机数生成
import yaml  # YAML配置文件处理
import math  # 数学函数
import re  # 正则表达式
import torch  # PyTorch深度学习框架
import transformers  # Hugging Face变换器库

from torch.utils.data import Dataset  # PyTorch数据集基类
from llava.constants import (
    IGNORE_INDEX,  # 忽略索引，用于损失计算
    DEFAULT_IMAGE_TOKEN,  # 默认图像标记
    DEFAULT_IM_START_TOKEN,  # 默认图像开始标记
    DEFAULT_IM_END_TOKEN,  # 默认图像结束标记
    IMAGE_TOKEN_INDEX  # 图像标记索引
)
from llava.train.config import DataArguments  # 数据参数配置
from llava import conversation as conversation_lib  # 对话处理库
from llava.mm_utils import (
    process_highres_image,  # 高分辨率图像处理
    process_anyres_image,  # 任意分辨率图像处理
    process_highres_image_crop_split,  # 高分辨率图像裁剪分割
    tokenizer_image_token  # 图像标记token化
)
from llava.utils import rank0_print, process_video_with_decord  # 工具函数

# 设置PyTorch多进程共享策略为文件系统，避免共享内存问题
torch.multiprocessing.set_sharing_strategy("file_system")

# 允许加载截断的图像文件，提高鲁棒性
ImageFile.LOAD_TRUNCATED_IMAGES = True

def _tokenize_fn(strings: Sequence[str], tokenizer: transformers.PreTrainedTokenizer) -> Dict:
    """
    对字符串列表进行token化处理

    这是多模态训练中文本处理的核心函数，将文本转换为模型可以理解的token序列。

    Args:
        strings: 要token化的字符串序列
        tokenizer: 预训练的分词器
    Returns:
        包含input_ids、labels和长度信息的字典
    """
    # 对每个字符串进行token化处理
    tokenized_list = [
        tokenizer(
            text,
            return_tensors="pt",  # 返回PyTorch张量
            padding="longest",  # 填充到最长序列的长度
            max_length=tokenizer.model_max_length,  # 最大长度限制
            truncation=True,  # 超长截断
        )
        for text in strings
    ]

    # 提取input_ids，同时作为labels使用（自回归语言建模）
    input_ids = labels = [tokenized.input_ids[0] for tokenized in tokenized_list]

    # 计算每个序列的实际长度（排除padding token）
    input_ids_lens = labels_lens = [
        tokenized.input_ids.ne(tokenizer.pad_token_id).sum().item()
        for tokenized in tokenized_list
    ]

    return dict(
        input_ids=input_ids,        # 输入token序列
        labels=labels,              # 标签序列（与input_ids相同）
        input_ids_lens=input_ids_lens,  # 输入序列长度
        labels_lens=labels_lens,    # 标签序列长度
    )

def _mask_targets(target, tokenized_lens, speakers):
    """
    掩码目标序列，只对助手的回答计算损失

    在对话训练中，我们只希望模型学习生成助手的回答，而不是用户的输入。
    因此需要将用户输入部分的标签设置为IGNORE_INDEX，在损失计算时忽略。

    Args:
        target: 目标序列（标签）
        tokenized_lens: 每个对话轮次的token长度列表
        speakers: 每个对话轮次的说话者列表
    """
    # 从第一个token化长度开始（跳过系统消息）
    cur_idx = tokenized_lens[0]
    tokenized_lens = tokenized_lens[1:]
    # 将开头部分设置为忽略索引
    target[:cur_idx] = IGNORE_INDEX

    # 遍历每个对话轮次
    for tokenized_len, speaker in zip(tokenized_lens, speakers):
        if speaker == "human":
            # 如果是人类说话，将该部分标签设为忽略索引（+2是为了跳过特殊token）
            target[cur_idx + 2 : cur_idx + tokenized_len] = IGNORE_INDEX
        cur_idx += tokenized_len

def _add_speaker_and_signal(header, source, get_conversation=True):
    """
    为每轮对话添加说话者标识和开始/结束信号

    这个函数将原始对话数据格式化为带有说话者标识的格式，
    便于模型理解对话的结构。

    Args:
        header: 对话头部信息
        source: 对话源数据
        get_conversation: 是否获取完整对话字符串
    Returns:
        格式化后的对话字符串
    """
    BEGIN_SIGNAL = "### "  # 开始信号
    END_SIGNAL = "\n"      # 结束信号
    conversation = header

    for sentence in source:
        from_str = sentence["from"]
        # 将说话者标识映射到标准角色名称
        if from_str.lower() == "human":
            from_str = conversation_lib.default_conversation.roles[0]  # 通常是"USER"
        elif from_str.lower() == "gpt":
            from_str = conversation_lib.default_conversation.roles[1]  # 通常是"ASSISTANT"
        else:
            from_str = "unknown"

        # 格式化句子：添加说话者标识和信号
        sentence["value"] = BEGIN_SIGNAL + from_str + ": " + sentence["value"] + END_SIGNAL
        if get_conversation:
            conversation += sentence["value"]

    # 在对话末尾添加开始信号，为下一轮对话做准备
    conversation += BEGIN_SIGNAL
    return conversation

def preprocess_qwen(sources, tokenizer: transformers.PreTrainedTokenizer, has_image: bool = False, max_len=2048, system_message: str = "You are an intelligent driving assistant.") -> Dict:
    """
    为Qwen模型预处理对话数据

    这个函数专门为Qwen模型格式化多模态对话数据，处理图像token、
    对话模板和标签掩码等关键步骤。

    Args:
        sources: 对话源数据列表
        tokenizer: Qwen分词器
        has_image: 是否包含图像
        max_len: 最大序列长度
        system_message: 系统消息
    Returns:
        包含处理后的input_ids和labels的字典
    """
    # 定义角色映射（注释掉的是旧版本的格式）
    roles = {"human": "user", "gpt": "assistant"}

    # 将图像token添加到分词器的特殊token中
    # 使用深拷贝避免修改原始分词器
    tokenizer = copy.deepcopy(tokenizer)
    # 当实际存在图像时，添加图像token作为特殊token
    if has_image:
        tokenizer.add_tokens([DEFAULT_IMAGE_TOKEN], special_tokens=True)

    # 获取各种特殊token的索引
    image_token_index = tokenizer.convert_tokens_to_ids(DEFAULT_IMAGE_TOKEN)
    # aeb_token_index = tokenizer.convert_tokens_to_ids(DEFAULT_AEB_TOKEN)  # AEB相关token（已注释）
    im_start, im_end = tokenizer.additional_special_tokens_ids  # 对话开始和结束token

    # 定义不需要掩码的token索引（在损失计算时不忽略）
    unmask_tokens_idx = [198, im_start, im_end]  # 198是换行符的token id
    nl_tokens = tokenizer("\n").input_ids  # 换行符的token序列

    # 重置Qwen的对话模板，避免每次应用时都包含系统消息
    # 这个模板定义了对话的格式：<|im_start|>角色\n内容<|im_end|>\n
    chat_template = "{% for message in messages %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}"
    tokenizer.chat_template = chat_template

    # 以下是注释掉的旧版本角色token定义
    # _system = tokenizer("system").input_ids + nl_tokens
    # _user = tokenizer("user").input_ids + nl_tokens
    # _assistant = tokenizer("assistant").input_ids + nl_tokens

    # 应用提示模板，处理每个对话样本
    input_ids, targets = [], []
    for i, source in enumerate(sources):
        # 如果第一个消息不是来自人类，跳过它（通常是系统消息）
        if roles[source[0]["from"]] != roles["human"]:
            source = source[1:]

        input_id, target = [], []

        # 新版本：使用apply_chat_template方法
        # 为每个句子构建系统消息
        input_id += tokenizer.apply_chat_template([{"role": "system", "content": system_message}])
        # 系统消息部分的标签设为忽略索引（不参与损失计算）
        target += [IGNORE_INDEX] * len(input_id)

        for conv in source:
            # Make sure llava data can load
            try:
                role = conv["role"]
                content = conv["content"]
            except:
                role = conv["from"]
                content = conv["value"]

            role =  roles.get(role, role)
            conv = [{"role" : role, "content" : content}]
            encode_id = tokenizer.apply_chat_template(conv)
            input_id += encode_id
            if role in ["user", "system"]:
                target += [IGNORE_INDEX] * len(encode_id)
            else:
                target += encode_id
        
        assert len(input_id) == len(target), f"{len(input_id)} != {len(target)}"
        for idx, encode_id in enumerate(input_id):
            if encode_id in unmask_tokens_idx:
                target[idx] = encode_id
            if encode_id == image_token_index:
                input_id[idx] = IMAGE_TOKEN_INDEX
            # if encode_id == aeb_token_index:
            #     input_id[idx] = AEB_TOKEN_INDEX
        input_ids.append(input_id)
        targets.append(target)
    input_ids = torch.tensor(input_ids, dtype=torch.long)
    targets = torch.tensor(targets, dtype=torch.long)

    return dict(
        input_ids=input_ids,  # tensor(bs x seq_len)
        labels=targets,  # tensor(bs x seq_len)
    )

def preprocess(sources: Sequence[str], tokenizer: transformers.PreTrainedTokenizer, has_image: bool = False) -> Dict:
    """
    Given a list of sources, each is a conversation list. This transform:
    1. Add signal '### ' at the beginning each sentence, with end signal '\n';
    2. Concatenate conversations together;
    3. Tokenize the concatenated conversation;
    4. Make a deepcopy as the target. Mask human words with IGNORE_INDEX.
    """
    if conversation_lib.default_conversation.version == "qwen":
        return preprocess_qwen(sources, tokenizer, has_image=has_image)
    # add end signal and concatenate together
    conversations = []
    for source in sources:
        header = f"{conversation_lib.default_conversation.system}\n\n"
        conversation = _add_speaker_and_signal(header, source)
        conversations.append(conversation)

    # tokenize conversations
    def get_tokenize_len(prompts):
        return [len(tokenizer_image_token(prompt, tokenizer)) for prompt in prompts]

    if has_image:
        input_ids = [tokenizer_image_token(prompt, tokenizer, return_tensors="pt") for prompt in conversations]
    else:
        conversations_tokenized = _tokenize_fn(conversations, tokenizer)
        input_ids = conversations_tokenized["input_ids"]

    targets = copy.deepcopy(input_ids)
    for target, source in zip(targets, sources):
        if has_image:
            tokenized_lens = get_tokenize_len([header] + [s["value"] for s in source])
        else:
            tokenized_lens = _tokenize_fn([header] + [s["value"] for s in source], tokenizer)["input_ids_lens"]
        speakers = [sentence["from"] for sentence in source]
        _mask_targets(target, tokenized_lens, speakers)

    return dict(input_ids=input_ids, labels=targets)

def preprocess_multimodal(sources: Sequence[str], data_args: DataArguments) -> Dict:
    is_multimodal = data_args.is_multimodal
    if not is_multimodal:
        return sources

    for source in sources:
        for sentence in source:
            # TODO maybe this should be changed for interleaved data?
            # if DEFAULT_IMAGE_TOKEN in sentence["value"] and not sentence["value"].startswith(DEFAULT_IMAGE_TOKEN):
            # only check for num_im=1
            num_im = len(re.findall(DEFAULT_IMAGE_TOKEN, sentence["value"]))
            if num_im == 1 and DEFAULT_IMAGE_TOKEN in sentence["value"] and not sentence["value"].startswith(DEFAULT_IMAGE_TOKEN):
                sentence["value"] = sentence["value"].replace(DEFAULT_IMAGE_TOKEN, "").strip()
                sentence["value"] = DEFAULT_IMAGE_TOKEN + "\n" + sentence["value"]
                sentence["value"] = sentence["value"].strip()
            replace_token = DEFAULT_IMAGE_TOKEN
            if data_args.mm_use_im_start_end:
                replace_token = DEFAULT_IM_START_TOKEN + replace_token + DEFAULT_IM_END_TOKEN
            sentence["value"] = sentence["value"].replace(DEFAULT_IMAGE_TOKEN, replace_token)

            # For videoInstruct-100k noisy_data. TODO: Ask Yuanhan to clean the data instead of leaving the noise code here.
            sentence["value"] = sentence["value"].replace("QA_GT_caption_based_noisy", "")

    return sources

class LazySupervisedDataset(Dataset):
    """
    懒加载监督数据集类

    这是LLaVA训练的核心数据集类，支持多模态数据的懒加载。
    "懒加载"意味着数据在需要时才被处理，而不是在初始化时全部加载到内存中，
    这样可以处理大规模数据集而不会耗尽内存。

    主要功能：
    - 支持图像和视频数据
    - 动态长度分组
    - 模态感知的数据处理
    - 错误重试机制
    """

    def __init__(self, tokenizer: transformers.PreTrainedTokenizer, data_args: DataArguments, split: str):
        """
        初始化数据集

        Args:
            tokenizer: 预训练分词器
            data_args: 数据参数配置
            split: 数据集分割（'train' 或 'val'）
        """
        super(LazySupervisedDataset, self).__init__()
        self.tokenizer = tokenizer

        # 根据分割类型选择数据路径
        if split == 'train':
            self.data_path = data_args.train_data_path
        elif split == 'val':
            self.data_path = data_args.eval_data_path

        # 加载数据字典列表
        with open(self.data_path, "r") as file:
            self.list_data_dict = json.load(file)

        rank0_print(f"从 {self.data_path} 加载了 {len(self.list_data_dict)} 个样本")
        self.tokenizer = tokenizer
        self.data_args = data_args

    def __len__(self):
        """返回数据集大小"""
        return len(self.list_data_dict)

    @property
    def lengths(self):
        """
        计算每个样本的长度
        用于长度分组采样，将相似长度的样本分组到同一批次

        Returns:
            每个样本长度的列表
        """
        length_list = []
        for sample in self.list_data_dict:
            # 如果包含图像，添加固定的图像token数量
            img_tokens = 128 if "image" in sample else 0
            # 计算对话的总词数加上图像token数
            length_list.append(sum(len(conv["value"].split()) for conv in sample["conversations"]) + img_tokens)
        return length_list

    @property
    def modality_lengths(self):
        """
        计算模态感知的长度

        多模态样本使用正数长度，纯文本样本使用负数长度。
        这种设计允许采样器区分不同模态的数据，进行更好的批次组织。

        Returns:
            模态感知的长度列表（正数=多模态，负数=纯文本）
        """
        length_list = []
        for sample in self.list_data_dict:
            # 计算对话的总词数
            cur_len = sum(len(conv["value"].split()) for conv in sample["conversations"])
            assert cur_len > 0, f"对话长度为0: {sample}"

            # 如果包含图像、视频或启用了早期文本混合，使用正数长度
            if "image" in sample or "video" in sample or self.data_args.early_mix_text:
                length_list.append(cur_len)
            else:
                # 纯文本样本使用负数长度
                length_list.append(-cur_len)
        return length_list

    def process_image(self, image_file, overwrite_image_aspect_ratio=None):
        """
        处理图像文件

        Args:
            image_file: 图像文件路径
            overwrite_image_aspect_ratio: 覆盖图像宽高比设置
        Returns:
            处理后的图像张量
        """
        # image_folder = self.data_args.image_folder  # 图像文件夹路径（已注释）
        processor = self.data_args.image_processor  # 图像处理器

        try:
            # 打开图像并转换为RGB格式
            image = Image.open(image_file).convert("RGB")
        except Exception as exn:
            print(f"无法打开图像 {image_file}. 异常:", exn)
            raise exn

        image_size = image.size
        image_aspect_ratio = self.data_args.image_aspect_ratio
        if overwrite_image_aspect_ratio is not None:
            image_aspect_ratio = overwrite_image_aspect_ratio
        if image_aspect_ratio == "highres":
            image = process_highres_image(image, self.data_args.image_processor, self.data_args.image_grid_pinpoints)
        elif image_aspect_ratio == "anyres" or "anyres_max" in image_aspect_ratio:
            image = process_anyres_image(image, self.data_args.image_processor, self.data_args.image_grid_pinpoints)
        elif image_aspect_ratio == "crop_split":
            image = process_highres_image_crop_split(image, self.data_args)
        elif image_aspect_ratio == "pad":

            def expand2square(pil_img, background_color):
                width, height = pil_img.size
                if width == height:
                    return pil_img
                elif width > height:
                    result = Image.new(pil_img.mode, (width, width), background_color)
                    result.paste(pil_img, (0, (width - height) // 2))
                    return result
                else:
                    result = Image.new(pil_img.mode, (height, height), background_color)
                    result.paste(pil_img, ((height - width) // 2, 0))
                    return result

            image = expand2square(image, tuple(int(x * 255) for x in processor.image_mean))
            image = processor.preprocess(image, return_tensors="pt")["pixel_values"][0]
        else:
            image = processor.preprocess(image, return_tensors="pt")["pixel_values"][0]
        return image, image_size, "image"

    def __getitem__(self, i) -> Dict[str, torch.Tensor]:
        # TODO: define number of retries somewhere else
        num_base_retries = 3
        num_final_retries = 300

        # try the current sample first
        for attempt_idx in range(num_base_retries):
            try:
                sample = self._get_item(i)
                return sample
            except Exception as e:
                # sleep 1s in case it is a cloud disk issue
                print(f"[Try #{attempt_idx}] Failed to fetch sample {i}. Exception:", e)
                time.sleep(1)

        # try other samples, in case it is file corruption issue
        for attempt_idx in range(num_base_retries):
            try:
                next_index = min(i + 1, len(self.list_data_dict) - 1)
                # sample_idx = random.choice(range(len(self)))
                sample = self._get_item(next_index)
                return sample
            except Exception as e:
                # no need to sleep
                print(f"[Try other #{attempt_idx}] Failed to fetch sample {next_index}. Exception:", e)
                pass

        try:
            sample = self._get_item(i)
            return sample
        except Exception as e:
            raise e

    def _get_item(self, i) -> Dict[str, torch.Tensor]:
        sources = self.list_data_dict[i]
        if isinstance(i, int):
            sources = [sources]
        assert len(sources) == 1, "Don't know why it is wrapped to a list"  # FIXME

        if "image" in sources[0]:
            image_file = self.list_data_dict[i]["image"]
            if type(image_file) is list:
                image = [self.process_image(f) for f in image_file]
                # Handling multi images
                # overwrite to process with simple pad 
                if len(image_file) > 1:
                    image = [self.process_image(f, "pad") for f in image_file]
                    image = [[im[0], im[1], "image"] for im in image]
            else:
                image = [self.process_image(image_file)]
            sources = preprocess_multimodal(copy.deepcopy([e["conversations"] for e in sources]), self.data_args)

        elif "video" in sources[0]:
            frame_files = self.list_data_dict[i]["video"]

            # Optionally, you can sample frames if you don't need all of them
            # num_frames_to_sample = 10
            # if len(frame_files) > num_frames_to_sample:
            #     sampled_indices = np.linspace(0, len(frame_files) - 1, num_frames_to_sample, dtype=int)
            #     frame_files = [frame_files[idx] for idx in sampled_indices]
            
            video_frames = []
            for frame_path in frame_files:
                try:
                    with Image.open(frame_path) as img:
                        frame = img.convert("RGB")
                        video_frames.append(frame)
                except IOError:
                    print(f"Failed to read frame at path: {frame_path}")

            processor = self.data_args.image_processor
            image = processor.preprocess(video_frames, return_tensors="pt")["pixel_values"]
            image = [(image, video_frames[0].size, "video")]
            sources = preprocess_multimodal(copy.deepcopy([e["conversations"] for e in sources]), self.data_args)
            
        else:
            sources = copy.deepcopy([e["conversations"] for e in sources])

        has_image = ("image" in self.list_data_dict[i]) or ("video" in self.list_data_dict[i])
        data_dict = preprocess(sources, self.tokenizer, has_image=has_image)

        if "prompt" in data_dict:
            prompt = data_dict["prompt"]
        else:
            prompt = None

        if isinstance(i, int):
            data_dict = dict(input_ids=data_dict["input_ids"][0], labels=data_dict["labels"][0])

        # image exist in the data
        if "image" in self.list_data_dict[i]:
            data_dict["image"] = image
        elif "video" in self.list_data_dict[i]:
            data_dict["image"] = image
        elif self.data_args.is_multimodal:
            # image does not exist in the data, but the model is multimodal
            crop_size = self.data_args.image_processor.crop_size
            data_dict["image"] = [
                (torch.zeros(1, 3, crop_size["height"], crop_size["width"]), (crop_size["width"], crop_size["height"]), "text"),
            ]
        # prompt exist in the data
        if prompt is not None:
            data_dict["prompt"] = prompt

        data_dict["id"] = self.list_data_dict[i].get("id", i)
        data_dict['action'] = torch.tensor(self.list_data_dict[i].get("action", [0, 0]))
        return data_dict

@dataclass
class DataCollatorForSupervisedDataset(object):
    """Collate examples for supervised fine-tuning."""

    tokenizer: transformers.PreTrainedTokenizer

    def pad_sequence(self, input_ids, batch_first, padding_value):
        if self.tokenizer.padding_side == "left":
            input_ids = [torch.flip(_input_ids, [0]) for _input_ids in input_ids]
        input_ids = torch.nn.utils.rnn.pad_sequence(input_ids, batch_first=batch_first, padding_value=padding_value)
        if self.tokenizer.padding_side == "left":
            input_ids = torch.flip(input_ids, [1])
        return input_ids

    def __call__(self, instances: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        input_ids, labels = tuple([instance[key] for instance in instances] for key in ("input_ids", "labels"))
        # input_ids, labels, ids = tuple([instance[key] for instance in instances] for key in ("input_ids", "labels", "id"))
        input_ids = [_input_ids[: self.tokenizer.model_max_length] for _input_ids in input_ids]
        labels = [_labels[: self.tokenizer.model_max_length] for _labels in labels]

        ego_action = [instance["action"] for instance in instances]
        if self.tokenizer.pad_token_id is None:
            # self.tokenizer.pad_token_id = self.tokenizer.eos_token_id  # FIXME: this could only be triggered for llama3 model.
            self.tokenizer.pad_token_id = 0 # This gets the best result. Don't know why.
        input_ids = self.pad_sequence(input_ids, batch_first=True, padding_value=self.tokenizer.pad_token_id)
        labels = self.pad_sequence(labels, batch_first=True, padding_value=IGNORE_INDEX)
        ego_action = torch.stack(ego_action) if ego_action[0] is not None else None

        batch = dict(input_ids=input_ids, labels=labels.long() if labels.dtype == torch.int32 else labels, attention_mask=input_ids.ne(self.tokenizer.pad_token_id), ego_action=ego_action)

        # batch = dict(input_ids=input_ids, labels=labels, attention_mask=input_ids.ne(self.tokenizer.pad_token_id), ids=ids)

        if "image" in instances[0]:
            images = [instance["image"] for instance in instances]

            batch["image_sizes"] = [im[1] for im_list in images for im in im_list]
            batch["modalities"] = [im[2] for im_list in images for im in im_list]
            images = [im[0] for im_list in images for im in im_list]

            # if all(x is not None and x.shape == images[0].shape for x in images):
                # Image: (N, P, C, H, W)
                # Video: (N, F, C, H, W)
            #     batch["images"] = torch.stack(images)
            # else:
            batch["images"] = images

        if "prompt" in instances[0]:
            batch["prompts"] = [instance["prompt"] for instance in instances]

        return batch
    
def make_supervised_data_module(tokenizer: transformers.PreTrainedTokenizer,
                                data_args) -> Dict:
    """Make dataset and collator for supervised fine-tuning."""
    train_dataset = LazySupervisedDataset(tokenizer=tokenizer,
                                data_args=data_args,
                                split='train')
    eval_dataset = LazySupervisedDataset(tokenizer=tokenizer,
                                data_args=data_args,
                                split='val')
    data_collator = DataCollatorForSupervisedDataset(tokenizer=tokenizer)
    return dict(train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                data_collator=data_collator)