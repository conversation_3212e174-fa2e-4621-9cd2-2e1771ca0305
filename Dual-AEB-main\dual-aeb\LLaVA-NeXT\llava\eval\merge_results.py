# 导入完成这个任务所需要的标准库
# json: 用于读取和写入JSON格式的文件，这是一种轻量级的数据交换格式，易于人阅读和编写，也易于机器解析和生成。
import json 
# os: 提供了很多与操作系统交互的功能，比如这里用来处理文件路径和列出目录内容。
import os 
# argparse: 这是Python标准库中推荐的命令行选项、参数和子命令解析器。通过它，我们可以方便地从命令行接收输入参数。
import argparse

# 定义一个名为 merge_results 的函数，它接收两个参数：
# input_dir: 存放着需要被合并的、零散的JSON结果文件的目录路径。
# output_file: 合并后生成的单一JSON文件的完整路径和文件名。
def merge_results(input_dir, output_file):
    # 程序逻辑：这个函数的核心任务是遍历一个指定的文件夹，
    # 找到所有符合特定命名规则（以 "_conversations.json" 结尾）的文件，
    # 将它们的内容逐一读取出来，追加到一个列表中，
    # 最后将这个包含了所有内容的列表写入一个新的、单一的JSON文件中。

    # 初始化一个空列表，名为 merged_results。
    # 这个列表将作为“容器”，用来存储从所有分块结果文件中读取出来的数据。
    merged_results = []
    
    # 语法重难点：os.listdir(input_dir)
    # 这是一个非常常用的函数，它会返回一个包含指定目录（input_dir）下所有文件和文件夹名称的列表。
    # for循环会遍历这个列表中的每一个文件名。
    for filename in os.listdir(input_dir):
        
        # 语法重难点：字符串方法 .endswith()
        # 这是一个条件判断语句。它检查当前遍历到的文件名(filename)是否以 "_conversations.json" 这个字符串结尾。
        # 这是一个简单有效的文件筛选方法，确保我们只处理特定类型的结果文件，避免误操作其他文件。
        if filename.endswith("_conversations.json"):
            
            # 语法重难点：os.path.join()
            # 这个函数会智能地将多个路径部分组合成一个单一的路径。
            # 它会自动处理不同操作系统下的路径分隔符（比如Windows的'\'和Linux的'/'），
            # 这使得代码具有更好的跨平台兼容性。
            file_path = os.path.join(input_dir, filename)
            
            # 语法重难点：with open(...) as ...
            # 这是Python中推荐的文件操作方式，被称为“上下文管理器”。
            # 'with'语句可以自动管理资源的获取和释放。在这里，它能确保文件在读取完毕后，
            # 无论过程中是否发生错误，都会被安全地关闭，从而避免资源泄露。
            # "r" 表示以只读模式（read-only）打开文件。
            with open(file_path, "r") as f:
                
                # 语法重难点：json.load()
                # 这个函数从一个打开的文件对象（f）中读取JSON格式的数据，
                # 并将其解析（反序列化）成一个Python对象（在这里通常是一个列表或字典）。
                data = json.load(f)
                
                # 语法重难点：列表方法 .extend()
                # 这个方法将一个可迭代对象（在这里是刚从文件中读取的列表 data）中的所有元素，
                # 逐一追加到另一个列表（merged_results）的末尾。
                # 这与 .append() 不同，.append() 会将整个列表作为一个单一元素添加进去。
                # 使用 .extend() 正是我们所需要的合并效果。
                merged_results.extend(data)
    
    # 同样使用 'with open(...)' 上下文管理器来写入文件。
    # "w" 表示以写入模式（write）打开文件。如果文件已存在，会覆盖其内容；如果不存在，则会创建新文件。
    with open(output_file, "w") as outfile:
        
        # 语法重难点：json.dump()
        # 这个函数将一个Python对象（merged_results 列表）序列化成JSON格式的字符串，并将其写入一个文件对象（outfile）。
        # indent=4: 这是一个非常实用的参数，它会让输出的JSON文件具有良好的格式化，
        # 使用4个空格进行缩进，使得文件内容层次分明，极大地提高了可读性。
        json.dump(merged_results, outfile, indent=4)
        
    # 使用f-string格式化字符串，向用户打印一条信息，
    # 告知他们合并后的文件已经保存到了哪个位置。这是一种友好的用户交互。
    print(f"Merged results saved to {output_file}")

# 语法重难点：if __name__ == "__main__":
# 这是一个Python脚本的经典入口点判断。
# 这行代码的意思是：只有当这个脚本是作为主程序直接被执行时（而不是被其他脚本导入时），
# 才执行它下面的代码块。这可以防止在被导入时意外执行代码，是模块化编程的好习惯。
if __name__ == "__main__":
    
    # 创建一个ArgumentParser对象，用于处理命令行参数。
    # description参数会在用户请求帮助信息（例如使用 -h 或 --help）时显示，用以简要描述这个脚本的功能。
    parser = argparse.ArgumentParser(description="Merge multiple result files into one.")
    
    # 向解析器中添加命令行参数的定义。
    # "--input_dir": 定义了一个名为 --input_dir 的命令行参数。
    # type=str: 指定了这个参数的值应该是字符串类型。
    # required=True: 表示这个参数是必须提供的，如果用户不提供，程序会报错。
    # help="...": 提供了该参数的帮助信息。
    parser.add_argument("--input_dir", type=str, required=True, help="Directory containing the result files.")
    parser.add_argument("--output_file", type=str, required=True, help="Path to the output merged JSON file.")
    
    # 解析命令行传入的参数。
    # 这行代码会读取命令行（如 python merge_results.py --input_dir /path/to/results --output_file /path/to/merged.json），
    # 并将解析后的参数值存放在一个类似字典的对象中（args）。
    # 之后可以通过 args.input_dir 和 args.output_file 来访问这些值。
    args = parser.parse_args()
    
    # 调用我们之前定义的核心功能函数 merge_results，
    # 并将从命令行解析得到的输入目录和输出文件路径作为参数传递给它。
    merge_results(args.input_dir, args.output_file)