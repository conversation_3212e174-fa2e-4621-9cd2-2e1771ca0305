# 导入 os 库，它提供了与操作系统交互的功能，这里主要用来检查文件路径是否存在。
import os
# 从当前目录下的 siglip_encoder.py 文件中导入 SigLipVisionTower 类。
# SigLipVisionTower 是一个封装了Google SigLIP视觉模型的类，负责将图像转换为特征向量。
from .siglip_encoder import SigLipVisionTower
# 从当前目录下的 clip_encoder.py 文件中导入 CLIPVisionTower 和 CLIPVisionTowerS2 类。
# 这两个类封装了OpenAI的CLIP视觉模型，同样用于将图像转换为特征向量。S2代表支持S2多尺度策略的版本。
from .clip_encoder import CLIPVisionTower, CLIPVisionTowerS2

# (被注释掉的代码，表明开发者可能曾尝试过集成其他的视觉模型，如EvaClip)
# from .eva_clip.eva_clip_encoder import EvaClipVisionTower
# from .dev_eva_clip.eva_vit import EvaViTWrapper


# 定义一个名为 build_vision_tower 的函数，这是一个工厂函数。
# 它接收两个参数：
# vision_tower_cfg: 这是一个配置对象，包含了所有关于视觉塔的设置信息。
# **kwargs: 这是一个字典，用于接收任何额外的关键字参数，增加了函数的灵活性。
def build_vision_tower(vision_tower_cfg, **kwargs):
    # 程序逻辑：这个函数的核心是一个if-elif-else的判断结构。
    # 它首先从配置中获取视觉塔的名称（例如 "openai/clip-vit-large-patch14-336" 或 "google/siglip-so400m-patch14-384"）。
    # 然后，它根据名称中的关键词（如 "clip" 或 "siglip"）来决定实例化哪一个具体的视觉塔类。

    # 语法重难点：getattr(object, name, default)
    # 这是一个安全地获取对象属性的方法。
    # 它会尝试获取 vision_tower_cfg 对象的 'mm_vision_tower' 属性。
    # 如果找不到，它会再次尝试获取 'vision_tower' 属性。
    # 如果还是找不到，它会返回 None，而不会报错。这使得代码对不同版本的配置格式有更好的兼容性。
    vision_tower = getattr(vision_tower_cfg, 'mm_vision_tower', getattr(vision_tower_cfg, 'vision_tower', None))
    
    # 检查提供的 vision_tower 名称是否是一个已经存在的本地文件路径。
    is_absolute_path_exists = os.path.exists(vision_tower)
    
    # 这是一个条件判断，用来确定是否是已知的、可加载的视觉模型。
    # 条件1: `is_absolute_path_exists`: 如果是本地路径，则尝试加载。
    # 条件2: `vision_tower.startswith("openai") or vision_tower.startswith("laion")`: 如果是来自OpenAI或LAION的预训练模型（通常是CLIP系列），则尝试加载。
    # 条件3: `"ShareGPT4V" in vision_tower`: 如果是特定的ShareGPT4V模型，也尝试加载。
    if is_absolute_path_exists or vision_tower.startswith("openai") or vision_tower.startswith("laion") or "ShareGPT4V" in vision_tower:
        
        # 检查视觉塔名称字符串中是否包含 "clip" 这个关键词。
        if 'clip' in vision_tower:
            # 如果包含，就实例化 CLIPVisionTower 类，并将配置和额外参数传给它。
            # 这将创建一个基于CLIP的视觉编码器。
            return CLIPVisionTower(vision_tower, args=vision_tower_cfg, **kwargs)
            
        # 检查视觉塔名称字符串中是否包含 "siglip" 这个关键词。
        elif "siglip" in vision_tower:
            # 如果包含，就实例化 SigLipVisionTower 类。
            # 这将创建一个基于SigLIP的视觉编码器。
            return SigLipVisionTower(vision_tower, vision_tower_cfg=vision_tower_cfg, **kwargs)
            
    # 语法重难点：raise ValueError
    # 如果以上所有条件都不满足，说明我们遇到了一个未知的、不支持的视觉塔名称。
    # 程序会主动抛出一个 ValueError 异常，并附带一条清晰的错误信息，
    # 这样可以立即终止程序的执行并告知开发者问题所在。
    raise ValueError(f"Unknown vision tower: {vision_tower}")