# 导入完成这个任务所需要的标准库
# json: 用于读取和写入JSON格式的文件。
import json
# os: 提供了与操作系统交互的功能，比如创建目录、检查路径是否存在。
import os
# argparse: 用于从命令行接收输入参数，让脚本更加灵活。
import argparse
# math: 提供了数学运算的功能，这里我们主要使用它的向上取整函数 ceil。
import math

# 定义一个名为 split_dataset 的函数，它接收三个参数：
# input_file: 需要被分割的原始JSON数据集文件的路径。
# output_dir: 用于存放分割后生成的所有小数据块文件的目录路径。
# num_chunks: 希望将原始数据集分割成的总份数。
def split_dataset(input_file, output_dir, num_chunks):
    # 程序逻辑：这个函数的核心任务是读取一个大的JSON文件，
    # 根据用户指定的份数（num_chunks）计算出每一份应该包含多少条数据，
    # 然后将原始数据切割成相应数量的小块，并将每一块分别存为一个新的JSON文件。

    # 使用 'with open(...)' 上下文管理器以只读模式（"r"）打开输入的JSON文件。
    # 这能确保文件在操作完成后被自动关闭。
    with open(input_file, "r") as f:
        # 使用 json.load() 将整个JSON文件的内容读取并解析成一个Python列表（data）。
        # 此时，整个数据集都被加载到了内存中。
        data = json.load(f)

    # 语法重难点：math.ceil() 和列表切片
    # 计算每一个小数据块（chunk）应该包含的数据条目数量。
    # len(data): 获取数据集的总条目数。
    # len(data) / num_chunks: 计算平均每个数据块的大小。
    # math.ceil(): 向上取整函数。例如，如果有101条数据要分成10块，101/10=10.1，向上取整后得到11。
    # 这样做可以确保所有数据都能被分完，即使总数不能被份数整除，最后一个文件可能会比其他文件小一些。
    chunk_size = math.ceil(len(data) / num_chunks)
    
    # 语法重难点：os.path.exists() 和 os.makedirs()
    # 检查指定的输出目录（output_dir）是否存在。
    # if not ...: 如果不存在...
    if not os.path.exists(output_dir):
        # ...则使用 os.makedirs() 创建这个目录。
        # 这个函数可以创建多级目录（例如 /path/to/new/folder），非常方便。
        os.makedirs(output_dir)

    # 语法重难点：range()
    # 这是一个 for 循环，它会从 0 循环到 num_chunks - 1。
    # 变量 i 在每次循环中会依次取值为 0, 1, 2, ..., 直到 num_chunks-1。
    for i in range(num_chunks):
        
        # 语法重难点：列表切片 (List Slicing)
        # 这是Python中非常强大和常用的功能，用于从列表中提取一个子集。
        # 语法是 `list[start:end]`，它会返回从索引 start 开始到 end-1 结束的所有元素。
        # i * chunk_size: 计算当前块的起始索引。
        # (i + 1) * chunk_size: 计算当前块的结束索引。
        # 例如，如果 chunk_size 是100：
        # 第一次循环 (i=0): 切片为 data[0:100]
        # 第二次循环 (i=1): 切片为 data[100:200]
        # ...以此类推
        chunk = data[i*chunk_size:(i+1)*chunk_size]
        
        # 使用f-string构建每个输出文件的文件名，格式为 "chunk_0.json", "chunk_1.json" 等。
        # 然后使用 os.path.join() 将目录路径和文件名安全地拼接在一起。
        chunk_file = os.path.join(output_dir, f"chunk_{i}.json")
        
        # 使用 'with open(...)' 上下文管理器以写入模式 ("w") 打开块文件。
        with open(chunk_file, "w") as outfile:
            
            # 使用 json.dump() 将当前的数据块（chunk）写入对应的文件中。
            # indent=4 使得输出的JSON文件格式化，方便人类阅读。
            json.dump(chunk, outfile, indent=4)
            
        # 打印一条提示信息，告诉用户当前是第几块，以及它被保存到了哪里。
        print(f"Chunk {i} saved to {chunk_file}")

# 脚本的主入口点。
if __name__ == "__main__":
    
    # 创建命令行参数解析器。
    parser = argparse.ArgumentParser(description="Split dataset into chunks.")
    
    # 定义脚本接受的三个命令行参数。
    parser.add_argument("--input_file", type=str, required=True, help="Path to the input dataset JSON file.")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save the output chunks.")
    # 注意这里 `type=int`，argparse会自动将命令行的字符串转为整数。
    parser.add_argument("--num_chunks", type=int, required=True, help="Number of chunks to split the dataset into.")
    
    # 解析从命令行传入的参数。
    args = parser.parse_args()
    
    # 调用核心功能函数，并传入解析到的参数。
    split_dataset(args.input_file, args.output_dir, args.num_chunks)