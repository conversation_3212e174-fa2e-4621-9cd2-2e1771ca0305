import os

AVAILABLE_MODELS = {
    "llava_llama": "LlavaLlamaForCausalLM, LlavaConfig",
    # "llava_gemma": "LlavaGemmaForCausalLM, LlavaGemmaConfig",
    "llava_qwen": "<PERSON><PERSON><PERSON><PERSON>ForCausalLM, LlavaQwenConfig",
    # "llava_qwen_moe": "LlavaQwenMoeForCausalLM, LlavaQwenMoeConfig",
    # "llava_mistral": "LlavaMistralForCausalLM, LlavaMistralConfig",
    # "llava_mixtral": "LlavaMixtralForCausalLM, LlavaMixtralConfig",
    # Add other models as needed
}

for model_name, model_classes in AVAILABLE_MODELS.items():
    try:
        exec(f"from .language_model.{model_name} import {model_classes}")
    except ImportError:
        import traceback

        traceback.print_exc()
        print(f"Failed to import {model_name} from llava.language_model.{model_name}")
        pass

from .language_model.llava_llama import <PERSON>lavaLlamaForCausalLM, LlavaConfig
from .language_model.llava_qwen import LlavaQwenForCausalLM, LlavaQwenConfig
