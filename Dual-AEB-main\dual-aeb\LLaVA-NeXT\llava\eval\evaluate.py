# 导入必要的库和模块
from llava.model.builder import load_pretrained_model  # 用于加载预训练的LLaVA模型
from llava.mm_utils import process_images, tokenizer_image_token  # 多模态工具：图像处理和token化
from llava.constants import IMAGE_TOKEN_INDEX  # 图像token的索引常量
from llava.conversation import conv_templates  # 对话模板，用于格式化输入
from random import shuffle  # 随机打乱数据
import torch, os  # PyTorch深度学习框架和操作系统接口
import json  # JSON数据处理
from PIL import Image  # Python图像处理库
import copy  # 深拷贝工具
import warnings  # 警告处理
from collections import defaultdict  # 默认字典，用于统计
import nltk  # 自然语言处理工具包
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction  # BLEU评估指标
from rouge_score import rouge_scorer  # ROUGE评估指标
from nltk.translate.meteor_score import meteor_score  # METEOR评估指标
from prettytable import PrettyTable  # 表格美化输出
import argparse  # 命令行参数解析
import gc  # 垃圾回收，用于内存管理
from tqdm import tqdm  # 进度条显示
warnings.filterwarnings("ignore")  # 忽略警告信息

def parse_args():
    """
    解析命令行参数的函数
    用于配置多GPU评估的各种参数
    """
    parser = argparse.ArgumentParser(description="Run evaluation on dataset chunks with multiple GPUs.")
    # 指定使用的GPU ID
    parser.add_argument('--gpu_id', type=int, required=True, help='ID of the GPU to use.')
    # 预训练模型的路径
    parser.add_argument('--pretrained', type=str, required=True, help='Path to the pretrained model.')
    # 测试数据集分块的JSON文件路径
    parser.add_argument('--test_dataset_chunk', type=str, required=True, help='Path to the dataset chunk JSON file.')
    # 实验名称，用于保存结果
    parser.add_argument('--exp_name', type=str, required=True, help='Experiment name for saving results.')
    # 是否使用B2D（Behavior to Decision）模式的标志
    parser.add_argument('--is_b2d', type=bool, default=True, help='Flag indicating whether to use B2D mode.')
    # GPU编号
    parser.add_argument('--num', type=int, required=True, help='Number of the GPU.')
    return parser.parse_args()

def load_model(pretrained, model_name, device_map="auto", device="cuda", overwrite_config=None):
    """
    加载预训练模型的函数
    Args:
        pretrained: 预训练模型路径
        model_name: 模型名称
        device_map: 设备映射策略，"auto"表示自动分配
        device: 目标设备，默认为"cuda"
        overwrite_config: 覆盖配置参数
    Returns:
        tokenizer: 分词器
        model: 加载的模型
        image_processor: 图像处理器
    """
    # 如果没有提供覆盖配置，使用默认配置
    if overwrite_config is None:
        # tie_word_embeddings: 是否共享词嵌入权重
        # use_cache: 是否使用缓存加速推理
        overwrite_config = {'tie_word_embeddings': True, 'use_cache': True}

    # 加载预训练模型的四个组件：分词器、模型、图像处理器、最大长度
    tokenizer, model, image_processor, max_length = load_pretrained_model(
        pretrained, model_name, device_map=device_map, overwrite_config=overwrite_config
    )
    # 将模型移动到指定设备（GPU）
    model.to(device)
    # 设置模型为评估模式，关闭dropout等训练特有的层
    model.eval()
    return tokenizer, model, image_processor

def extract_frames(video_path):
    """
    从视频路径列表中提取帧图像
    Args:
        video_path: 包含帧图像路径的列表
    Returns:
        frames: 提取的RGB格式帧图像列表
    """
    frames = []
    # 遍历每个帧的路径
    for frame_path in video_path:
        try:
            # 使用with语句确保文件正确关闭，避免内存泄漏
            with Image.open(frame_path) as img:
                # 将图像转换为RGB格式，确保颜色通道一致性
                frames.append(img.convert("RGB"))
        except IOError:
            # 捕获IO异常，打印错误信息但不中断程序
            print(f"Failed to read frame at path: {frame_path}")
    return frames

def generate_text(model, tokenizer, prompt_question, image_tensors, image_sizes, output_actions=False, device="cuda"):
    """
    使用模型生成文本回答
    Args:
        model: 预训练的多模态模型
        tokenizer: 分词器
        prompt_question: 输入的提示问题
        image_tensors: 图像张量列表
        image_sizes: 图像尺寸列表
        output_actions: 是否输出动作，默认False
        device: 计算设备
    Returns:
        生成的文本回答
    """
    # 将提示问题转换为token ID，并处理图像token
    # unsqueeze(0)添加batch维度，to(device)移动到GPU
    input_ids = tokenizer_image_token(prompt_question, tokenizer, IMAGE_TOKEN_INDEX, return_tensors="pt").unsqueeze(0).to(device)

    # 使用模型生成回答
    cont = model.generate(
        input_ids,                # 输入的token ID
        images=image_tensors,     # 图像张量
        image_sizes=image_sizes,  # 图像尺寸
        do_sample=False,          # 不使用采样，确保结果确定性
        temperature=0,            # 温度为0，选择概率最高的token
        max_new_tokens=4096,      # 最大生成token数
        output_actions=output_actions  # 是否输出动作
    )
    # 解码生成的token为文本，跳过特殊token，返回第一个结果
    return tokenizer.batch_decode(cont, skip_special_tokens=True)[0]

def calculate_bleu4(reference, candidate):
    """
    计算BLEU-4评估指标
    BLEU用于评估机器翻译和文本生成质量，基于n-gram精确度
    Args:
        reference: 参考答案（真实标签）
        candidate: 候选答案（模型预测）
    Returns:
        BLEU-4分数（0-1之间，越高越好）
    """
    # 使用平滑函数method4处理短句子的BLEU计算问题
    smoothie = SmoothingFunction().method4
    # weights=(0.25, 0.25, 0.25, 0.25)表示1-gram到4-gram的权重相等
    return sentence_bleu([reference.split()], candidate.split(), weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothie)

def calculate_rouge_l(reference, candidate):
    """
    计算ROUGE-L评估指标
    ROUGE-L基于最长公共子序列，评估文本的召回率和精确度
    Args:
        reference: 参考答案
        candidate: 候选答案
    Returns:
        ROUGE-L的F1分数
    """
    # 创建ROUGE评分器，use_stemmer=True表示使用词干提取
    scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
    scores = scorer.score(reference, candidate)
    # 返回F-measure（F1分数），平衡精确度和召回率
    return scores['rougeL'].fmeasure

def calculate_meteor(reference, candidate):
    """
    计算METEOR评估指标
    METEOR考虑了精确度、召回率和词序，比BLEU更全面
    Args:
        reference: 参考答案
        candidate: 候选答案
    Returns:
        METEOR分数（0-1之间，越高越好）
    """
    # 将文本分词为token列表
    reference_tokens = reference.split()
    candidate_tokens = candidate.split()
    # 计算METEOR分数，参考答案需要包装在列表中
    return meteor_score([reference_tokens], candidate_tokens)

def save_conversations(conversations, output_path):
    """
    保存对话数据到JSON文件
    Args:
        conversations: 对话数据列表
        output_path: 输出文件路径
    """
    with open(output_path, "w") as outfile:
        # indent=4使JSON格式化输出，便于阅读
        json.dump(conversations, outfile, indent=4)

def save_results_as_table(results, filename="evaluation_results.csv"):
    """
    将评估结果保存为表格格式的CSV文件
    Args:
        results: 评估结果列表
        filename: 输出文件名
    """
    # 创建美观的表格对象
    table = PrettyTable()
    table.field_names = ["Metric", "Average Score"]

    # 定义需要计算平均值的评估指标
    metrics = [
        "description_bleu4", "description_rouge_l", "description_meteor",  # 描述任务的三个指标
        "bbox_bleu4", "bbox_rouge_l", "bbox_meteor",                      # 边界框任务的三个指标
        "action_bleu4", "action_rouge_l", "action_meteor",                # 动作任务的三个指标
    ]

    # 初始化每个指标的累计值为0，使用字典推导式
    metric_sums = {metric: 0 for metric in metrics}
    num_results = len(results) - 1  # 排除overall结果，只计算单个样本的结果数量

    # 遍历所有结果，累加每个指标的分数（排除overall统计结果）
    for result in results:
        if result["id"] != "overall":  # 确保不处理overall统计结果
            for metric in metrics:
                metric_sums[metric] += result[metric]

    # 计算每个指标的平均分数并添加到表格中
    for metric in metrics:
        average_score = metric_sums[metric] / num_results
        # 格式化为4位小数
        table.add_row([metric, f"{average_score:.4f}"])

    # 手动添加overall的精确度和召回率
    # 使用列表推导式和next()函数找到overall结果
    overall_result = [result for result in results if result["id"] == "overall"][0]
    table.add_row(["action_precision", f"{overall_result['action_precision']:.4f}"])
    table.add_row(["action_recall", f"{overall_result['action_recall']:.4f}"])

    # 将表格写入CSV文件
    with open(filename, "w") as file:
        file.write(table.get_csv_string())

def calculate_precision_recall(action_answer_gt, action_answer_pred):
    """
    计算精确度和召回率所需的混淆矩阵元素
    用于评估动作预测的二分类性能（是否需要刹车）
    Args:
        action_answer_gt: 真实标签答案
        action_answer_pred: 模型预测答案
    Returns:
        true_positive: 真正例（正确预测为正类）
        false_positive: 假正例（错误预测为正类）
        false_negative: 假负例（错误预测为负类）
    """
    # 检查真实标签中是否包含"brak"（刹车），转为小写避免大小写问题
    gt_positive = "brak" in action_answer_gt.lower()
    # 检查预测结果中是否包含"brak"
    pred_positive = "brak" in action_answer_pred.lower()

    # 计算混淆矩阵的各个元素
    true_positive = gt_positive and pred_positive      # 真实为正，预测也为正
    false_positive = not gt_positive and pred_positive # 真实为负，预测为正
    false_negative = gt_positive and not pred_positive # 真实为正，预测为负

    return true_positive, false_positive, false_negative


def main(is_b2d=True):
    """
    主评估函数，执行完整的模型评估流程
    Args:
        is_b2d: 是否使用B2D（Behavior to Decision）模式
    """
    # 指定使用的模型名称
    model_name = "llava_qwen"
    # 加载预训练模型的三个组件
    tokenizer, model, image_processor = load_model(args.pretrained, model_name)

    # 读取测试数据集分块
    with open(args.test_dataset_chunk, "r") as f:
        test_dataset_chunk = json.load(f)

    # 初始化结果存储列表
    results = []        # 存储评估指标结果
    conversations = []  # 存储对话数据

    # 初始化全局统计变量，用于计算overall精确度和召回率
    total_true_positive = 0   # 总真正例数
    total_false_positive = 0  # 总假正例数
    total_false_negative = 0  # 总假负例数

    # 使用tqdm显示进度条，遍历每个测试样本
    for sample in tqdm(test_dataset_chunk, desc=f"Evaluating on GPU {args.gpu_id}"):
        # 从样本中提取视频帧图像
        video_frames = extract_frames(sample["image"])  # video
        # 使用图像处理器处理帧图像，转换为模型输入格式
        image_tensors = process_images(video_frames, image_processor, model.config)
        # 将图像张量转换为半精度浮点数并移动到GPU，节省显存
        image_tensors = [_image.to(dtype=torch.float16, device="cuda") for _image in image_tensors]
        # 获取每帧图像的尺寸信息
        image_sizes = [frame.size for frame in video_frames]

        # 解析对话数据，提取三个任务的问题和答案
        convs = sample["conversations"]
        # 描述任务：问题和真实答案
        des_questions, des_answers_gt = convs[0]["value"], convs[1]["value"]
        # 边界框任务：问题和真实答案
        bbox_questions, bbox_answers_gt = convs[2]["value"], convs[3]["value"]
        # 动作任务：问题和真实答案
        act_questions, act_answers_gt = convs[4]["value"], convs[5]["value"]

        # === 第一阶段：描述任务评估 ===
        conv_template = "qwen_1_5"  # 使用Qwen 1.5对话模板
        # 深拷贝对话模板，避免修改原始模板
        conv = copy.deepcopy(conv_templates[conv_template])
        # 添加用户问题（角色0）和空的助手回答（角色1）
        conv.append_message(conv.roles[0], des_questions)
        conv.append_message(conv.roles[1], None)
        # 生成格式化的提示文本
        prompt_question = conv.get_prompt()

        # 生成描述任务的预测答案
        des_answer_pred = generate_text(model, tokenizer, prompt_question, image_tensors, image_sizes, device=device)

        # 计算描述任务的三个评估指标
        des_bleu4 = calculate_bleu4(des_answers_gt, des_answer_pred)
        des_rouge_l = calculate_rouge_l(des_answers_gt, des_answer_pred)
        des_meteor = calculate_meteor(des_answers_gt, des_answer_pred)

        # === 第二阶段：边界框任务评估 ===
        # 重新创建对话模板，包含描述任务的完整对话历史
        conv = copy.deepcopy(conv_templates[conv_template])
        conv.append_message(conv.roles[0], des_questions)      # 描述问题
        conv.append_message(conv.roles[1], des_answer_pred)    # 描述答案（使用预测结果）
        conv.append_message(conv.roles[0], bbox_questions)     # 边界框问题
        conv.append_message(conv.roles[1], None)               # 待生成的边界框答案
        prompt_question = conv.get_prompt()

        # 生成边界框任务的预测答案
        bbox_answers_pred = generate_text(model, tokenizer, prompt_question, image_tensors, image_sizes, device=device)

        # 计算边界框任务的三个评估指标
        bbox_bleu4 = calculate_bleu4(bbox_answers_gt, bbox_answers_pred)
        bbox_rouge_l = calculate_rouge_l(bbox_answers_gt, bbox_answers_pred)
        bbox_meteor = calculate_meteor(bbox_answers_gt, bbox_answers_pred)

        # === 第三阶段：动作任务评估 ===
        # 创建包含完整对话历史的模板（描述+边界框+动作）
        conv = copy.deepcopy(conv_templates[conv_template])
        conv.append_message(conv.roles[0], des_questions)      # 描述问题
        conv.append_message(conv.roles[1], des_answer_pred)    # 描述答案
        conv.append_message(conv.roles[0], bbox_questions)     # 边界框问题
        conv.append_message(conv.roles[1], bbox_answers_pred)  # 边界框答案
        conv.append_message(conv.roles[0], act_questions)      # 动作问题
        conv.append_message(conv.roles[1], None)               # 待生成的动作答案
        prompt_question = conv.get_prompt()

        # 生成动作任务的预测答案
        act_answer_pred = generate_text(model, tokenizer, prompt_question, image_tensors, image_sizes, device=device)

        # 计算动作任务的三个评估指标
        act_bleu4 = calculate_bleu4(act_answers_gt, act_answer_pred)
        act_rouge_l = calculate_rouge_l(act_answers_gt, act_answer_pred)
        act_meteor = calculate_meteor(act_answers_gt, act_answer_pred)

        # 计算精确度和召回率的混淆矩阵元素
        true_positive, false_positive, false_negative = calculate_precision_recall(act_answers_gt, act_answer_pred)

        # 累加全局统计量，用于计算overall指标
        total_true_positive += true_positive
        total_false_positive += false_positive
        total_false_negative += false_negative


        # === B2D模式：行为到决策的数值预测 ===
        if is_b2d:
            # 构建完整的对话历史，包含所有三个任务的问答
            conv = copy.deepcopy(conv_templates[conv_template])
            conv.append_message(conv.roles[0], des_questions)      # 描述问题
            conv.append_message(conv.roles[1], des_answer_pred)    # 描述答案
            conv.append_message(conv.roles[0], bbox_questions)     # 边界框问题
            conv.append_message(conv.roles[1], bbox_answers_pred)  # 边界框答案
            conv.append_message(conv.roles[0], act_questions)      # 动作问题
            conv.append_message(conv.roles[1], act_answer_pred)    # 动作答案
            prompt_question = conv.get_prompt()

            # 获取真实的动作标签（数值形式）
            action_gt = sample["action"]
            # 将提示转换为token ID并移动到GPU
            intput_ids = tokenizer_image_token(prompt_question, tokenizer, IMAGE_TOKEN_INDEX, return_tensors="pt").unsqueeze(0).to(device)
            # 将真实动作转换为张量格式
            action_gt = torch.tensor(action_gt, dtype=torch.float16, device=device).unsqueeze(0)

            # 准备多模态输入，获取模型内部表示所需的各种输入
            # 这是LLaVA模型处理图像和文本混合输入的关键步骤
            (intput_ids, position_ids, attention_mask, _, inputs_embeds, _) = model.prepare_inputs_labels_for_multimodal(
                intput_ids, None, None, None, None, image_tensors, modalities="image", image_sizes=image_sizes
            )

            # 通过模型获取隐藏状态，output_hidden_states=True返回所有层的隐藏状态
            # [-1]取最后一层的隐藏状态
            hidden_states = model.model(
                input_ids=intput_ids,
                position_ids=position_ids,
                attention_mask=attention_mask,
                inputs_embeds=inputs_embeds,
                output_hidden_states=True
            ).hidden_states[-1]

            # 计算加权平均的隐藏状态表示
            if attention_mask is not None:
                # 扩展attention_mask的维度以匹配hidden_states
                extended_attention_mask = attention_mask.unsqueeze(-1).expand(hidden_states.size())
                # 应用attention mask，忽略padding部分
                masked_hidden_states = hidden_states * extended_attention_mask
                # 计算有效token的隐藏状态总和
                sum_hidden_states = torch.sum(masked_hidden_states, dim=1, keepdim=True)
                # 计算有效token的数量
                sum_attention_mask = torch.sum(extended_attention_mask, dim=1, keepdim=True)
                # 计算加权平均隐藏状态
                average_hidden_states = sum_hidden_states / sum_attention_mask
            else:
                # 如果没有attention_mask，直接计算平均值
                average_hidden_states = torch.mean(hidden_states, dim=1, keepdim=True)

            # 使用AEB解码器预测动作数值
            # decode_aeb是模型的自动紧急制动解码器
            _, action_pred = model.decode_aeb(average_hidden_states, action_gt)

            # B2D模式：保存包含数值动作预测的对话数据
            conversations.append({
                "id": sample["id"],                                              # 样本ID
                "image": sample["image"],                                        # 视频帧路径
                "description_question": des_questions,                          # 描述任务问题
                "description_answer_gt": des_answers_gt,                        # 描述任务真实答案
                "description_answer_pred": des_answer_pred,                     # 描述任务预测答案
                "bbox_question": bbox_questions,                                # 边界框任务问题
                "bbox_answer_gt": bbox_answers_gt,                              # 边界框任务真实答案
                "bbox_answer_pred": bbox_answers_pred,                          # 边界框任务预测答案
                "action_question": act_questions,                               # 动作任务问题
                "action_answer_gt": act_answers_gt,                             # 动作任务真实答案
                "action_answer_pred": act_answer_pred,                          # 动作任务预测答案
                # 数值动作标签和预测：从GPU张量转换为CPU numpy数组再提取标量值
                "action_gt": action_gt[0].detach().cpu().numpy()[1].item(),     # 真实动作数值（索引1）
                "action_pred": action_pred.detach().cpu().numpy()[0].item()     # 预测动作数值
            })
        else:
            # 非B2D模式：只保存文本形式的对话数据
            conversations.append({
                "id": sample["id"],                                              # 样本ID
                "image": sample["image"],                                        # 视频帧路径
                "description_question": des_questions,                          # 描述任务问题
                "description_answer_gt": des_answers_gt,                        # 描述任务真实答案
                "description_answer_pred": des_answer_pred,                     # 描述任务预测答案
                "bbox_question": bbox_questions,                                # 边界框任务问题
                "bbox_answer_gt": bbox_answers_gt,                              # 边界框任务真实答案
                "bbox_answer_pred": bbox_answers_pred,                          # 边界框任务预测答案
                "action_question": act_questions,                               # 动作任务问题
                "action_answer_gt": act_answers_gt,                             # 动作任务真实答案
                "action_answer_pred": act_answer_pred                           # 动作任务预测答案
            })
        # 保存当前样本的所有评估指标结果
        results.append({
            "id": sample["id"],                    # 样本ID
            "description_bleu4": des_bleu4,       # 描述任务BLEU-4分数
            "description_rouge_l": des_rouge_l,   # 描述任务ROUGE-L分数
            "description_meteor": des_meteor,     # 描述任务METEOR分数
            "bbox_bleu4": bbox_bleu4,             # 边界框任务BLEU-4分数
            "bbox_rouge_l": bbox_rouge_l,         # 边界框任务ROUGE-L分数
            "bbox_meteor": bbox_meteor,           # 边界框任务METEOR分数
            "action_bleu4": act_bleu4,            # 动作任务BLEU-4分数
            "action_rouge_l": act_rouge_l,        # 动作任务ROUGE-L分数
            "action_meteor": act_meteor,          # 动作任务METEOR分数
        })

        # === 内存管理：释放GPU内存，防止内存溢出 ===
        del image_tensors    # 删除图像张量
        del hidden_states    # 删除隐藏状态
        del intput_ids       # 删除输入ID
        torch.cuda.empty_cache()  # 清空CUDA缓存
        gc.collect()         # 强制垃圾回收

    # === 计算overall精确度和召回率 ===
    # 精确度 = 真正例 / (真正例 + 假正例)，避免除零错误
    overall_precision = total_true_positive / (total_true_positive + total_false_positive) if (total_true_positive + total_false_positive) > 0 else 0.0
    # 召回率 = 真正例 / (真正例 + 假负例)，避免除零错误
    overall_recall = total_true_positive / (total_true_positive + total_false_negative) if (total_true_positive + total_false_negative) > 0 else 0.0

    # 添加overall统计结果到结果列表
    results.append({
        "id": "overall",                          # 标识为overall结果
        "description_bleu4": None,                # 单项指标设为None
        "description_rouge_l": None,
        "description_meteor": None,
        "bbox_bleu4": None,
        "bbox_rouge_l": None,
        "bbox_meteor": None,
        "action_bleu4": None,
        "action_rouge_l": None,
        "action_meteor": None,
        "action_precision": overall_precision,    # overall精确度
        "action_recall": overall_recall           # overall召回率
    })

    # === 保存结果文件 ===
    # 保存对话数据为JSON格式
    save_conversations(conversations, output_path=f"./exp/{args.exp_name}/gpu_{args.num}_conversations.json")
    # 保存评估结果为CSV表格格式
    save_results_as_table(results, filename=f"./exp/{args.exp_name}/gpu_{args.num}_evaluation_results.csv")

    # === 最终内存清理 ===
    del model                # 删除模型对象
    torch.cuda.empty_cache() # 清空CUDA缓存

if __name__ == "__main__":
    """
    程序入口点
    解析命令行参数并启动主评估流程
    """
    args = parse_args()      # 解析命令行参数
    device = "cuda"          # 设置计算设备为CUDA

    main()                   # 启动主评估函数