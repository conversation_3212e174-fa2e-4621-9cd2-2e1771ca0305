import torch
import torch.nn as nn
# 导入 re 库，用于处理正则表达式，这是一种强大的字符串匹配工具。
import re

# 从当前目录下的 pooler_projector.py 文件中导入 PoolerProjector 类。
# 这表明存在一种基于“池化”的投影层实现方式。
from .pooler_projector import PoolerProjector


# 定义一个名为 IdentityMap 的类。
# 程序逻辑：这是一个最简单的“投影层”，它实际上什么也不做，直接按原样返回输入。
# 在某些实验或模型设计中，如果视觉特征和文本特征的维度恰好相同，
# 或者研究者想测试不加任何变换的效果时，就会使用这种“恒等映射”。
class IdentityMap(nn.Module):
    def __init__(self):
        super().__init__()

    # forward 方法直接返回输入 x，忽略任何其他参数。
    def forward(self, x, *args, **kwargs):
        return x

    # @property 装饰器将一个方法变成一个只读属性。
    # 这里定义了 config 属性，返回一个字典，表明当前投影层的类型是 "identity"。
    @property
    def config(self):
        return {"mm_projector_type": "identity"}


# 定义一个名为 SimpleResBlock 的类，它实现了一个简单的残差块（Residual Block）。
# 程序逻辑：残差块是深度神经网络（如ResNet）中的一个核心组件。
# 它的思想是让网络学习输入的“残差”（即变化量），而不是直接学习从输入到输出的完整映射。
# 这通过一个“捷径连接”（skip connection）来实现，即把输入直接加到块的输出上。
# 这样做可以极大地缓解深度网络中的梯度消失问题，让训练更深的网络成为可能。
class SimpleResBlock(nn.Module):
    def __init__(self, channels):
        super().__init__()
        # 在进行变换之前，先对输入进行层归一化（Layer Normalization），可以稳定训练过程。
        self.pre_norm = nn.LayerNorm(channels)

        # 定义一个包含两个线性层和一个GELU激活函数的序列，这是残差块的核心变换部分。
        self.proj = nn.Sequential(nn.Linear(channels, channels), nn.GELU(), nn.Linear(channels, channels))

    def forward(self, x):
        # 先对输入 x 进行归一化。
        x_norm = self.pre_norm(x)
        # 语法重难点：残差连接 (Residual Connection)
        # 将归一化后的输入 x_norm 通过核心变换网络 self.proj，
        # 然后将变换后的结果与原始的输入 x 相加。
        # 注意：这里加的是原始的 x，而不是归一化后的 x_norm。
        return x + self.proj(x_norm) # 修正：通常是 x + self.proj(x_norm) 或 self.pre_norm(x + self.proj(x))


# 定义核心的工厂函数 build_vision_projector。
# 它根据配置动态地创建并返回不同类型的视觉投影层。
def build_vision_projector(config, delay_load=False, **kwargs):
    # 从配置对象中安全地获取 'mm_projector_type' 的值，如果不存在，则默认为 "linear"。
    projector_type = getattr(config, "mm_projector_type", "linear")

    # 如果类型是 "linear"，则创建一个简单的线性层。
    # nn.Linear(in_features, out_features)
    # config.mm_hidden_size: 视觉特征的维度。
    # config.hidden_size: 语言模型期望的输入维度。
    if projector_type == "linear":
        return nn.Linear(config.mm_hidden_size, config.hidden_size)

    # 如果类型是 "pooler"，则创建 PoolerProjector 实例。
    if projector_type == "pooler":
        return PoolerProjector(config, kwargs["vision_cfg"])

    # 语法重难点：正则表达式 re.match()
    # re.match 尝试从字符串的开头匹配一个模式。
    # r"^mlp(\d+)x_gelu$": 这是一个正则表达式。
    # ^: 匹配字符串的开头。
    # mlp: 匹配字面上的 "mlp"。
    # (\d+): 匹配一个或多个数字，并将其捕获为一个组（group）。
    # x_gelu: 匹配字面上的 "x_gelu"。
    # $: 匹配字符串的结尾。
    # 这个表达式可以匹配 "mlp2x_gelu", "mlp4x_gelu" 等字符串。
    mlp_gelu_match = re.match(r"^mlp(\d+)x_gelu$", projector_type)
    if mlp_gelu_match:
        # 如果匹配成功，说明需要创建一个多层感知机（MLP）。
        # mlp_gelu_match.group(1): 提取出捕获到的数字，即MLP的深度。
        mlp_depth = int(mlp_gelu_match.group(1))
        # 初始化一个模块列表，第一个模块是连接视觉和语言维度的线性层。
        modules = [nn.Linear(config.mm_hidden_size, config.hidden_size)]
        # 根据指定的深度，添加后续的GELU激活层和线性层。
        for _ in range(1, mlp_depth):
            modules.append(nn.GELU())
            modules.append(nn.Linear(config.hidden_size, config.hidden_size))
        # 语法重难点：nn.Sequential(*modules)
        # `*modules` 将列表 `modules` 中的所有元素解包，作为独立的参数传给 nn.Sequential。
        # nn.Sequential 会将这些模块按照传入的顺序串联起来，形成一个单一的神经网络模块。
        return nn.Sequential(*modules)

    # 使用正则表达式匹配更复杂的投影层类型，例如 "mlp2x_res2x_gelu"。
    mlp_gelu_resnet_match = re.match(r"^mlp(\d+)x_res(\d+)x_gelu$", projector_type)
    if mlp_gelu_resnet_match:
        # 如果匹配成功，则提取MLP的深度和残差块的数量。
        mlp_depth = int(mlp_gelu_resnet_match.group(1))
        res_depth = int(mlp_gelu_resnet_match.group(2))
        # 先构建MLP部分。
        modules = [nn.Linear(config.mm_hidden_size, config.hidden_size)]
        for _ in range(1, mlp_depth):
            modules.append(nn.GELU())
            modules.append(nn.Linear(config.hidden_size, config.hidden_size))
        # 然后在MLP后面追加指定数量的残差块。
        for _ in range(res_depth):
            modules.append(SimpleResBlock(config.hidden_size))
        # 最后将所有模块串联起来。
        return nn.Sequential(*modules)

    # 如果类型是 "identity"，返回 IdentityMap 实例。
    if projector_type == "identity":
        return IdentityMap()

    # 如果以上所有条件都不满足，说明遇到了未知的投影层类型，抛出异常。
    raise ValueError(f"Unknown projector type: {projector_type}")