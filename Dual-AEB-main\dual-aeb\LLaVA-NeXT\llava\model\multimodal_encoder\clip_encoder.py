import torch
import torch.nn as nn
# 从自定义的工具库中导入一个只在主进程（rank 0）打印信息的函数，用于避免在分布式训练中日志混乱。
from llava.utils import rank0_print
# 从Hugging Face的transformers库中导入CLIP模型的核心组件。
from transformers import CLIPVisionModel, CLIPImageProcessor, CLIPVisionConfig

# 语法重难点：try...except ImportError
# 这是一个防御性编程技巧。它尝试导入一个名为 s2wrapper 的库。
try:
    # 如果导入成功，就从中获取 multiscale_forward 函数。这个函数是实现S2多尺度策略的关键。
    from s2wrapper import forward as multiscale_forward
except:
    # 如果导入失败（比如用户没有安装这个库），程序不会报错退出，而是简单地跳过。
    # 这使得代码在没有安装可选依赖项的情况下也能保持部分功能可用。
    pass


# 定义 CLIPVisionTower 类，它封装了CLIP视觉模型，作为多模态模型的“视觉编码器”。
class CLIPVisionTower(nn.Module):
    # 构造函数，在创建类的实例时被调用。
    # vision_tower: 预训练CLIP模型的名称或本地路径，例如 "openai/clip-vit-large-patch14-336"。
    # args: 包含所有模型配置参数的对象。
    # delay_load: 一个布尔值，决定是否在初始化时立即加载模型权重。
    def __init__(self, vision_tower, args, delay_load=False):
        super().__init__()

        # 一个标志位，用于跟踪模型权重是否已经被加载。
        self.is_loaded = False

        # 保存模型名称和相关的配置参数。
        self.vision_tower_name = vision_tower
        # self.select_layer: 指定使用CLIP模型哪一层的输出作为特征。-2表示倒数第二层。
        self.select_layer = args.mm_vision_select_layer
        # self.select_feature: 指定如何提取特征。"patch"表示只使用图像块（patch）的特征，
        # "cls_patch"表示同时使用[CLS] token和图像块的特征。
        self.select_feature = getattr(args, "mm_vision_select_feature", "patch")

        # 程序逻辑：延迟加载（Lazy Loading）
        # 这是一种优化策略，避免在不需要时过早地占用大量内存/显存。
        if not delay_load:
            # 如果不延迟加载，则立即加载模型。
            rank0_print(f"Loading vision tower: {vision_tower}")
            self.load_model()
        # 以下两个elif条件检查是否需要微调视觉塔。如果是，也需要立即加载模型权重。
        elif getattr(args, "unfreeze_mm_vision_tower", False):
            rank0_print(f"The checkpoint seems to contain `vision_tower` weights: `unfreeze_mm_vision_tower`: True.")
            self.load_model()
        elif hasattr(args, "mm_tunable_parts") and "mm_vision_tower" in args.mm_tunable_parts:
            rank0_print(f"The checkpoint seems to contain `vision_tower` weights: `mm_tunable_parts` contains `mm_vision_tower`.")
            self.load_model()
        else:
            # 如果延迟加载且不需要微调，则只加载模型的配置信息（不加载权重），这样可以快速获取模型参数（如隐藏层大小）。
            self.cfg_only = CLIPVisionConfig.from_pretrained(self.vision_tower_name)

    # 负责加载实际的模型权重和图像处理器。
    def load_model(self, device_map=None):
        # 如果模型已经加载过了，就打印一条信息并直接返回，避免重复加载。
        if self.is_loaded:
            rank0_print("{} is already loaded, `load_model` called again, skipping.".format(self.vision_tower_name))
            return

        # 从Hugging Face Hub加载与模型匹配的图像处理器。
        self.image_processor = CLIPImageProcessor.from_pretrained(self.vision_tower_name)
        # 从Hugging Face Hub加载预训练的CLIP视觉模型。
        self.vision_tower = CLIPVisionModel.from_pretrained(self.vision_tower_name, device_map=device_map)
        # 语法重难点：requires_grad_(False)
        # 这是一个非常重要的操作。它会遍历模型的所有参数，并将它们的 requires_grad 属性设置为 False。
        # 这意味着在训练过程中，这些参数的梯度将不会被计算，即“冻结”了视觉塔，使其不参与训练。
        # 这是多模态模型训练中常用的策略，可以节省大量计算资源。
        self.vision_tower.requires_grad_(False)

        # 更新加载状态标志位。
        self.is_loaded = True

    # 根据配置选择并处理从CLIP模型中提取的特征。
    def feature_select(self, image_forward_outs):
        select_feature_type = self.select_feature

        # 程序逻辑：特征拼接
        # 这部分代码允许从CLIP的不同层提取特征并将它们拼接起来，以获得更丰富的表示。
        if self.select_feature in ["slicefour_patch", "slicefour_cls_patch"]:
            # "slicefour": 将模型的层数分成4等份，从每个等份中选择一层进行拼接。
            select_every_k_layer = len(image_forward_outs.hidden_states) // 4
            image_features = torch.cat([image_forward_outs.hidden_states[i] for i in range(select_every_k_layer + self.select_layer, len(image_forward_outs.hidden_states), select_every_k_layer)], dim=-1)
            select_feature_type = select_feature_type.replace("slicefour_", "")
        elif self.select_feature in ["slice_m25811_f6_patch", "slice_m25811_f6_cls_patch"]:
            # "slice_m25811_f6": 根据预先定义好的层索引列表进行选择和拼接。
            select_layers = [-2, -5, -8, -11, 6]
            image_features = torch.cat([image_forward_outs.hidden_states[i] for i in select_layers], dim=-1)
            select_feature_type = select_feature_type.replace("slice_m25811_f6_", "")
        else:
            # 默认情况：只从指定的某一层（self.select_layer）提取特征。
            image_features = image_forward_outs.hidden_states[self.select_layer]

        # 程序逻辑：选择 [CLS] Token 或 Patch Tokens
        # ViT (Vision Transformer) 模型的输出通常包含一个特殊的 [CLS] token（代表整个图像的全局特征）
        # 和一系列代表图像各个小块（patch）的特征。
        if select_feature_type == "patch":
            # 只选择patch tokens，忽略第一个token ([CLS] token)。
            image_features = image_features[:, 1:]
        elif select_feature_type == "cls_patch":
            # 同时保留 [CLS] token 和 patch tokens。
            image_features = image_features
        else:
            raise ValueError(f"Unexpected select feature: {select_feature_type}")
        return image_features

    # 定义模型的前向传播逻辑。
    def forward(self, images):
        # 检查输入是单个图像张量还是一个图像张量列表。
        if type(images) is list:
            image_features = []
            for image in images:
                # 对列表中的每个图像单独进行处理。
                # .to(device=self.device, dtype=self.dtype): 将数据移动到正确的设备（如GPU）和数据类型（如float16）。
                # .unsqueeze(0): 增加一个batch维度。
                # output_hidden_states=True: 要求模型返回所有中间层的隐藏状态，以便我们可以选择。
                image_forward_out = self.vision_tower(image.to(device=self.device, dtype=self.dtype).unsqueeze(0), output_hidden_states=True)
                # 调用 feature_select 方法来提取所需的特征。
                image_feature = self.feature_select(image_forward_out).to(image.dtype)
                image_features.append(image_feature)
        else:
            # 如果输入是单个张量（一个batch的图像），则直接进行批处理。
            image_forward_outs = self.vision_tower(images.to(device=self.device, dtype=self.dtype), output_hidden_states=True)
            image_features = self.feature_select(image_forward_outs).to(images.dtype)

        return image_features

    # 语法重难点：@property 装饰器
    # 这个装饰器可以将一个方法变成一个“属性”，使得我们可以像访问变量一样调用它（例如 `model.dtype` 而不是 `model.dtype()`）。
    
    # 返回一个虚拟的特征张量，用于某些初始化或调试场景。
    @property
    def dummy_feature(self):
        return torch.zeros(1, self.hidden_size, device=self.device, dtype=self.dtype)

    # 安全地获取模型的数据类型。
    @property
    def dtype(self):
        return self.vision_tower.dtype

    # 安全地获取模型所在的设备。
    @property
    def device(self):
        return self.vision_tower.device

    # 返回模型的配置对象。
    @property
    def config(self):
        if self.is_loaded:
            return self.vision_tower.config
        else:
            # 如果模型权重未加载，则返回仅包含配置信息的对象。
            return self.cfg_only

    # 计算并返回最终输出特征的维度大小。
    @property
    def hidden_size(self):
        _hidden_size = self.config.hidden_size
        # 如果使用了特征拼接，隐藏层大小会相应地乘以拼接的层数。
        if "slicefour" in self.select_feature:
            _hidden_size *= 4
        if "slice_m25811_f6" in self.select_feature:
            _hidden_size *= 5
        return _hidden_size

    # 计算图像一边被分成了多少个patch。
    @property
    def num_patches_per_side(self):
        return self.config.image_size // self.config.patch_size

    # 计算图像总共有多少个patch。
    @property
    def num_patches(self):
        _num_patches = (self.config.image_size // self.config.patch_size) ** 2
        if "cls_patch" in self.select_feature:
            _num_patches += 1 # 如果包含[CLS] token，则总数加1。
        return _num_patches

    # 返回CLIP模型期望的输入图像尺寸。
    @property
    def image_size(self):
        return self.config.image_size


# 定义CLIPVisionTowerS2类，它继承自上面的CLIPVisionTower。
# 程序逻辑：这个子类重写了父类的一些方法，以实现S2多尺度图像处理功能。
class CLIPVisionTowerS2(CLIPVisionTower):
    def __init__(self, vision_tower, args, delay_load=False):
        # 解析S2策略所需的多尺度分辨率参数。
        self.s2_scales = getattr(args, "s2_scales", "336,672,1008")
        self.s2_scales = list(map(int, self.s2_scales.split(",")))
        self.s2_scales.sort()
        self.s2_split_size = self.s2_scales[0]
        self.s2_image_size = self.s2_scales[-1]

        # 调用父类的构造函数。
        super().__init__(vision_tower, args, delay_load)

        # 关键修改：重写图像处理器的尺寸参数，
        # 将其设置为S2尺度中的最大尺寸，以确保能处理高分辨率输入。
        if not delay_load or getattr(args, "unfreeze_mm_vision_tower", False):
            self.image_processor.size["shortest_edge"] = self.s2_image_size
            self.image_processor.crop_size["height"] = self.image_processor.crop_size["width"] = self.s2_image_size

    # 重写 load_model 方法，以确保在加载模型时也更新图像处理器的尺寸。
    def load_model(self, device_map=None):
        # ... (与父类逻辑相同)
        super().load_model(device_map=device_map) # 这里可以直接调用父类的方法
        # 确保图像处理器的尺寸被设置为S2的最大尺寸。
        self.image_processor.size["shortest_edge"] = self.s2_image_size
        self.image_processor.crop_size["height"] = self.image_processor.crop_size["width"] = self.s2_image_size
        self.is_loaded = True

    # 这是一个辅助方法，用于对单个尺度的图像进行特征提取。
    def forward_feature(self, images):
        image_forward_outs = self.vision_tower(images.to(device=self.device, dtype=self.dtype), output_hidden_states=True)
        image_features = self.feature_select(image_forward_outs).to(images.dtype)
        return image_features

    # 重写 forward 方法，以实现S2多尺度处理。
    def forward(self, images):
        if type(images) is list:
            # (处理列表输入的逻辑)
            pass
        else:
            # 核心步骤：调用 s2wrapper 库的 multiscale_forward 函数。
            # 这个函数会自动将输入的图像 `images` 处理成 `s2_scales` 中定义的多个尺度，
            # 然后对每个尺度的图像调用 `self.forward_feature` 来提取特征，
            # 最后将所有尺度的特征拼接起来，返回一个更丰富的多尺度特征表示。
            image_features = multiscale_forward(self.forward_feature, images, img_sizes=self.s2_scales, max_split_size=self.s2_split_size, split_forward=True)

        return image_features

    # 重写 hidden_size 属性，因为S2策略的输出维度是单尺度维度的N倍（N是尺度的数量）。
    @property
    def hidden_size(self):
        return self.config.hidden_size * len(self.s2_scales)